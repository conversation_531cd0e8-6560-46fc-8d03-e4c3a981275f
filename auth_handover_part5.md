# Developer Handover Document: Authentication & Authorization Services (Part 5)

*Continuation from Part 4...*

```javascript
    async evaluateRule(rule, userId, context) {
        const ruleEngine = new RuleEngine();
        
        try {
            // Parse rule conditions
            const conditions = JSON.parse(rule.conditions);
            
            // Get user context data
            const userContext = await this.getUserContext(userId);
            const fullContext = { ...context, ...userContext };
            
            // Evaluate each condition
            for (const condition of conditions) {
                const result = await ruleEngine.evaluate(condition, fullContext);
                if (!result) {
                    return {
                        allowed: false,
                        reason: 'CONTEXTUAL_RULE_FAILED',
                        ruleId: rule.rule_id,
                        failedCondition: condition
                    };
                }
            }
            
            return { allowed: true };
        } catch (error) {
            console.error('Rule evaluation error:', error);
            return {
                allowed: false,
                reason: 'RULE_EVALUATION_ERROR',
                error: error.message
            };
        }
    }

    async getUserContext(userId) {
        const cacheKey = `user_context:${userId}`;
        const cached = await this.cache.redis.get(cacheKey);
        
        if (cached) {
            return JSON.parse(cached);
        }

        // Build user context
        const user = await User.findByPk(userId, {
            include: [
                { model: UserRole, include: [Role] },
                { model: Department },
                { model: Location }
            ]
        });

        const context = {
            userId: user.user_id,
            email: user.email,
            departmentId: user.department_id,
            locationId: user.location_id,
            roles: user.UserRoles.map(ur => ur.Role.role_name),
            permissions: await this.getUserPermissions(userId),
            accountCreatedDays: Math.floor((Date.now() - user.created_at.getTime()) / (1000 * 60 * 60 * 24)),
            lastLoginDays: user.last_login ? Math.floor((Date.now() - user.last_login.getTime()) / (1000 * 60 * 60 * 24)) : null
        };

        // Cache for 5 minutes
        await this.cache.redis.setex(cacheKey, 300, JSON.stringify(context));
        
        return context;
    }

    async createDynamicPermission(userId, resource, action, conditions, expiresAt = null) {
        const permission = await DynamicPermission.create({
            user_id: userId,
            resource: resource,
            action: action,
            conditions: JSON.stringify(conditions),
            created_at: new Date(),
            expires_at: expiresAt,
            created_by: userId,
            is_active: true
        });

        // Clear user permission cache
        await this.clearUserPermissionCache(userId);

        return permission;
    }

    async revokeDynamicPermission(permissionId, revokedBy) {
        const permission = await DynamicPermission.findByPk(permissionId);
        if (!permission) {
            throw new Error('Permission not found');
        }

        await permission.update({
            is_active: false,
            revoked_at: new Date(),
            revoked_by: revokedBy
        });

        // Clear user permission cache
        await this.clearUserPermissionCache(permission.user_id);

        return permission;
    }
}

module.exports = DynamicPermissionService;
```

### Resource-Level Security Implementation

```javascript
// services/ResourceSecurityService.js
class ResourceSecurityService {
    constructor() {
        this.cache = new CacheService();
    }

    async checkResourceAccess(userId, resourceType, resourceId, action) {
        // Get user permissions
        const permissionService = new PermissionService();
        const hasBasePermission = await permissionService.hasPermission(userId, resourceType, action);
        
        if (!hasBasePermission) {
            return { allowed: false, reason: 'NO_BASE_PERMISSION' };
        }

        // Apply resource-specific filters
        switch (resourceType) {
            case 'INVENTORY_ITEM':
                return await this.checkInventoryItemAccess(userId, resourceId, action);
            case 'LOCATION':
                return await this.checkLocationAccess(userId, resourceId, action);
            case 'CATEGORY':
                return await this.checkCategoryAccess(userId, resourceId, action);
            case 'SUPPLIER':
                return await this.checkSupplierAccess(userId, resourceId, action);
            case 'ORDER':
                return await this.checkOrderAccess(userId, resourceId, action);
            case 'REPORT':
                return await this.checkReportAccess(userId, resourceId, action);
            default:
                return { allowed: true };
        }
    }

    async checkInventoryItemAccess(userId, itemId, action) {
        const item = await InventoryItem.findByPk(itemId, {
            include: [
                { model: Category },
                { model: Location },
                { model: Supplier }
            ]
        });

        if (!item) {
            return { allowed: false, reason: 'RESOURCE_NOT_FOUND' };
        }

        const user = await User.findByPk(userId);
        
        // Location-based access control
        if (user.location_id && item.location_id !== user.location_id) {
            const hasMultiLocationAccess = await this.hasPermission(userId, 'INVENTORY', 'ACCESS_ALL_LOCATIONS');
            if (!hasMultiLocationAccess) {
                return { allowed: false, reason: 'LOCATION_ACCESS_DENIED' };
            }
        }

        // Category-based access control
        const categoryPermissions = await this.getCategoryPermissions(userId);
        if (categoryPermissions.length > 0) {
            const hasAccessToCategory = categoryPermissions.some(cp => 
                cp.category_id === item.category_id && cp.actions.includes(action)
            );
            if (!hasAccessToCategory) {
                return { allowed: false, reason: 'CATEGORY_ACCESS_DENIED' };
            }
        }

        // Value-based restrictions (for high-value items)
        if (item.unit_cost > 10000 && action === 'DELETE') {
            const hasHighValueAccess = await this.hasPermission(userId, 'INVENTORY', 'DELETE_HIGH_VALUE_ITEMS');
            if (!hasHighValueAccess) {
                return { allowed: false, reason: 'HIGH_VALUE_ITEM_PROTECTION' };
            }
        }

        return { allowed: true };
    }

    async checkLocationAccess(userId, locationId, action) {
        const user = await User.findByPk(userId);
        const location = await Location.findByPk(locationId);

        if (!location) {
            return { allowed: false, reason: 'RESOURCE_NOT_FOUND' };
        }

        // User's primary location access
        if (user.location_id === locationId) {
            return { allowed: true };
        }

        // Check for multi-location access
        const hasMultiLocationAccess = await this.hasPermission(userId, 'LOCATION', 'ACCESS_ALL_LOCATIONS');
        if (!hasMultiLocationAccess) {
            return { allowed: false, reason: 'LOCATION_ACCESS_DENIED' };
        }

        // Regional access control
        if (location.parent_location_id) {
            const userLocationHierarchy = await this.getLocationHierarchy(user.location_id);
            const targetLocationHierarchy = await this.getLocationHierarchy(locationId);
            
            const hasCommonParent = userLocationHierarchy.some(ul => 
                targetLocationHierarchy.includes(ul)
            );
            
            if (!hasCommonParent) {
                const hasRegionalAccess = await this.hasPermission(userId, 'LOCATION', 'ACCESS_ALL_REGIONS');
                if (!hasRegionalAccess) {
                    return { allowed: false, reason: 'REGIONAL_ACCESS_DENIED' };
                }
            }
        }

        return { allowed: true };
    }

    async checkOrderAccess(userId, orderId, action) {
        const order = await Order.findByPk(orderId, {
            include: [
                { model: User, as: 'CreatedBy' },
                { model: User, as: 'AssignedTo' },
                { model: Location }
            ]
        });

        if (!order) {
            return { allowed: false, reason: 'RESOURCE_NOT_FOUND' };
        }

        const user = await User.findByPk(userId);

        // Order creator access
        if (order.created_by === userId) {
            return { allowed: true };
        }

        // Assigned user access
        if (order.assigned_to === userId) {
            const allowedActions = ['READ', 'UPDATE_STATUS', 'ADD_COMMENT'];
            if (allowedActions.includes(action)) {
                return { allowed: true };
            }
        }

        // Manager access (can access orders in their location)
        if (user.location_id === order.location_id) {
            const isManager = await this.hasRole(userId, 'MANAGER');
            if (isManager) {
                return { allowed: true };
            }
        }

        // Department-based access
        const userDepartment = await Department.findByPk(user.department_id);
        if (userDepartment && userDepartment.can_access_all_orders) {
            return { allowed: true };
        }

        // Order value restrictions
        if (order.total_amount > 50000 && ['APPROVE', 'DELETE'].includes(action)) {
            const hasHighValueAccess = await this.hasPermission(userId, 'ORDER', 'MANAGE_HIGH_VALUE_ORDERS');
            if (!hasHighValueAccess) {
                return { allowed: false, reason: 'HIGH_VALUE_ORDER_PROTECTION' };
            }
        }

        return { allowed: false, reason: 'ORDER_ACCESS_DENIED' };
    }

    async getLocationHierarchy(locationId) {
        const cacheKey = `location_hierarchy:${locationId}`;
        const cached = await this.cache.redis.get(cacheKey);
        
        if (cached) {
            return JSON.parse(cached);
        }

        const hierarchy = [];
        let currentLocationId = locationId;

        while (currentLocationId) {
            const location = await Location.findByPk(currentLocationId);
            if (!location) break;
            
            hierarchy.push(currentLocationId);
            currentLocationId = location.parent_location_id;
        }

        // Cache for 1 hour
        await this.cache.redis.setex(cacheKey, 3600, JSON.stringify(hierarchy));
        
        return hierarchy;
    }

    async getCategoryPermissions(userId) {
        const cacheKey = `category_permissions:${userId}`;
        const cached = await this.cache.redis.get(cacheKey);
        
        if (cached) {
            return JSON.parse(cached);
        }

        const permissions = await CategoryPermission.findAll({
            where: { user_id: userId, is_active: true },
            include: [Category]
        });

        const result = permissions.map(cp => ({
            category_id: cp.category_id,
            category_name: cp.Category.category_name,
            actions: JSON.parse(cp.allowed_actions)
        }));

        // Cache for 30 minutes
        await this.cache.redis.setex(cacheKey, 1800, JSON.stringify(result));
        
        return result;
    }

    async filterResourcesByAccess(userId, resources, resourceType) {
        const accessibleResources = [];
        
        for (const resource of resources) {
            const accessCheck = await this.checkResourceAccess(
                userId, 
                resourceType, 
                resource.id || resource[`${resourceType.toLowerCase()}_id`], 
                'READ'
            );
            
            if (accessCheck.allowed) {
                accessibleResources.push(resource);
            }
        }
        
        return accessibleResources;
    }

    async getAccessibleLocations(userId) {
        const user = await User.findByPk(userId);
        const allLocations = await Location.findAll({ where: { is_active: true } });
        
        const accessibleLocations = [];
        
        for (const location of allLocations) {
            const accessCheck = await this.checkLocationAccess(userId, location.location_id, 'READ');
            if (accessCheck.allowed) {
                accessibleLocations.push(location);
            }
        }
        
        return accessibleLocations;
    }

    async hasPermission(userId, resource, action) {
        const permissionService = new PermissionService();
        return await permissionService.hasPermission(userId, resource, action);
    }

    async hasRole(userId, roleName) {
        const roleService = new RoleService();
        return await roleService.hasRole(userId, roleName);
    }
}

module.exports = ResourceSecurityService;
```

### API Route Protection Middleware

```javascript
// middleware/authMiddleware.js
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');

class AuthMiddleware {
    constructor() {
        this.cache = new (require('../services/CacheService'))();
        this.sessionService = new (require('../services/SessionService'))();
        this.auditService = new (require('../services/AuditService'))();
        this.ipSecurityService = new (require('../services/IPSecurityService'))();
    }

    // JWT Authentication Middleware
    authenticate = async (req, res, next) => {
        try {
            const token = this.extractToken(req);
            
            if (!token) {
                return res.status(401).json({
                    success: false,
                    message: 'Access token required'
                });
            }

            // Verify JWT token
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            
            // Check if token is blacklisted
            const isBlacklisted = await this.cache.redis.get(`blacklist:${token}`);
            if (isBlacklisted) {
                return res.status(401).json({
                    success: false,
                    message: 'Token has been revoked'
                });
            }

            // Validate session if session-based auth is enabled
            if (decoded.sessionToken) {
                const session = await this.sessionService.validateSession(decoded.sessionToken);
                if (!session) {
                    return res.status(401).json({
                        success: false,
                        message: 'Session expired or invalid'
                    });
                }
            }

            // Get user details
            const user = await User.findByPk(decoded.userId, {
                include: [
                    { model: UserRole, include: [Role] },
                    { model: Department }
                ]
            });

            if (!user || !user.is_active) {
                return res.status(401).json({
                    success: false,
                    message: 'User account is inactive'
                });
            }

            // Check account lockout
            if (user.locked_until && user.locked_until > new Date()) {
                return res.status(423).json({
                    success: false,
                    message: 'Account is temporarily locked',
                    lockedUntil: user.locked_until
                });
            }

            // Attach user to request
            req.user = user;
            req.token = token;
            req.sessionToken = decoded.sessionToken;

            next();
        } catch (error) {
            if (error.name === 'JsonWebTokenError') {
                return res.status(401).json({
                    success: false,
                    message: 'Invalid token'
                });
            }
            
            if (error.name === 'TokenExpiredError') {
                return res.status(401).json({
                    success: false,
                    message: 'Token expired'
                });
            }

            console.error('Authentication error:', error);
            return res.status(500).json({
                success: false,
                message: 'Authentication failed'
            });
        }
    };

    // Permission-based Authorization Middleware
    authorize = (resource, action, options = {}) => {
        return async (req, res, next) => {
            try {
                const userId = req.user.user_id;
                const permissionService = new (require('../services/PermissionService'))();
                
                // Check base permission
                const hasPermission = await permissionService.hasPermission(userId, resource, action);
                
                if (!hasPermission) {
                    await this.auditService.logAuthEvent(userId, 'PERMISSION_DENIED', {
                        resource: resource,
                        action: action,
                        endpoint: req.originalUrl,
                        method: req.method
                    });

                    return res.status(403).json({
                        success: false,
                        message: 'Insufficient permissions'
                    });
                }

                // Resource-level authorization if resourceId is provided
                if (options.resourceParam) {
                    const resourceId = req.params[options.resourceParam] || req.body[options.resourceParam];
                    
                    if (resourceId) {
                        const resourceSecurityService = new (require('../services/ResourceSecurityService'))();
                        const accessCheck = await resourceSecurityService.checkResourceAccess(
                            userId, 
                            resource, 
                            resourceId, 
                            action
                        );

                        if (!accessCheck.allowed) {
                            await this.auditService.logAuthEvent(userId, 'RESOURCE_ACCESS_DENIED', {
                                resource: resource,
                                resourceId: resourceId,
                                action: action,
                                reason: accessCheck.reason
                            });

                            return res.status(403).json({
                                success: false,
                                message: 'Access to this resource is denied',
                                reason: accessCheck.reason
                            });
                        }
                    }
                }

                next();
            } catch (error) {
                console.error('Authorization error:', error);
                return res.status(500).json({
                    success: false,
                    message: 'Authorization check failed'
                });
            }
        };
    };

    // Role-based Authorization Middleware
    requireRole = (roles) => {
        const roleArray = Array.isArray(roles) ? roles : [roles];
        
        return async (req, res, next) => {
            try {
                const userRoles = req.user.UserRoles.map(ur => ur.Role.role_name);
                const hasRequiredRole = roleArray.some(role => userRoles.includes(role));
                
                if (!hasRequiredRole) {
                    await this.auditService.logAuthEvent(req.user.user_id, 'ROLE_ACCESS_DENIED', {
                        requiredRoles: roleArray,
                        userRoles: userRoles,
                        endpoint: req.originalUrl
                    });

                    return res.status(403).json({
                        success: false,
                        message: 'Insufficient role privileges'
                    });
                }

                next();
            } catch (error) {
                console.error('Role authorization error:', error);
                return res.status(500).json({
                    success: false,
                    message: 'Role check failed'
                });
            }
        };
    };

    // MFA Requirement Middleware
    requireMFA = async (req, res, next) => {
        try {
            const user = req.user;
            
            // Check if MFA is required for this user
            if (user.mfa_enabled) {
                const mfaVerified = req.headers['x-mfa-verified'] === 'true';
                const mfaToken = req.headers['x-mfa-token'];
                
                if (!mfaVerified || !mfaToken) {
                    return res.status(422).json({
                        success: false,
                        message: 'MFA verification required',
                        requiresMFA: true
                    });
                }

                // Verify MFA token
                const mfaService = new (require('../services/MFAService'))();
                const isValidMFA = await mfaService.verifyTOTP(user.user_id, mfaToken);
                
                if (!isValidMFA) {
                    return res.status(422).json({
                        success: false,
                        message: 'Invalid MFA token'
                    });
                }
            }

            next();
        } catch (error) {
            console.error('MFA verification error:', error);
            return res.status(500).json({
                success: false,
                message: 'MFA verification failed'
            });
        }
    };

    // IP Security Middleware
    ipSecurity = async (req, res, next) => {
        try {
            const ipAddress = this.getClientIP(req);
            const userId = req.user ? req.user.user_id : null;
            
            const accessCheck = await this.ipSecurityService.checkIPAccess(ipAddress, userId);
            
            if (!accessCheck.allowed) {
                // Log blocked attempt
                await this.auditService.logSecurityEvent('IP_ACCESS_BLOCKED', {
                    ipAddress: ipAddress,
                    userId: userId,
                    reason: accessCheck.reason,
                    endpoint: req.originalUrl
                });

                const responseData = {
                    success: false,
                    message: 'Access denied from this location'
                };

                if (accessCheck.blockExpiresAt) {
                    responseData.blockExpiresAt = accessCheck.blockExpiresAt;
                }

                if (accessCheck.retryAfter) {
                    responseData.retryAfter = accessCheck.retryAfter;
                }

                return res.status(403).json(responseData);
            }

            next();
        } catch (error) {
            console.error('IP security check error:', error);
            next(); // Allow request to continue on error
        }
    };

    // Device Trust Middleware
    deviceTrust = async (req, res, next) => {
        try {
            const deviceFingerprint = req.headers['x-device-fingerprint'];
            
            if (!deviceFingerprint) {
                return res.status(400).json({
                    success: false,
                    message: 'Device fingerprint required'
                });
            }

            const deviceService = new (require('../services/DeviceService'))();
            const riskAssessment = await deviceService.checkDeviceRisk(
                req.user.user_id,
                deviceFingerprint,
                this.getClientIP(req)
            );

            // High-risk devices require additional verification
            if (riskAssessment.riskLevel === 'HIGH') {
                const additionalAuth = req.headers['x-additional-auth'];
                
                if (!additionalAuth) {
                    return res.status(422).json({
                        success: false,
                        message: 'Additional authentication required for new device',
                        riskLevel: riskAssessment.riskLevel,
                        riskFactors: riskAssessment.riskFactors,
                        requiresAdditionalAuth: true
                    });
                }

                // Verify additional authentication (could be email verification, SMS, etc.)
                // Implementation depends on your additional auth method
            }

            // Attach device info to request
            req.device = riskAssessment.device;
            req.deviceRisk = riskAssessment;

            next();
        } catch (error) {
            console.error('Device trust check error:', error);
            next(); // Allow request to continue on error
        }
    };

    // Rate Limiting Middleware
    createRateLimit = (options = {}) => {
        const defaultOptions = {
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 100, // limit each IP to 100 requests per windowMs
            message: {
                success: false,
                message: 'Too many requests, please try again later'
            },
            standardHeaders: true,
            legacyHeaders: false,
            keyGenerator: (req) => {
                // Use user ID if authenticated, otherwise fall back to IP
                return req.user ? `user:${req.user.user_id}` : `ip:${this.getClientIP(req)}`;
            },
            handler: async (req, res) => {
                // Log rate limit exceeded
                await this.auditService.logSecurityEvent('RATE_LIMIT_EXCEEDED', {
                    userId: req.user ? req.user.user_id : null,
                    ipAddress: this.getClientIP(req),
                    endpoint: req.originalUrl,
                    userAgent: req.get('User-Agent')
                });

                res.status(429).json(options.message || defaultOptions.message);
            }
        };

        return rateLimit({ ...defaultOptions, ...options });
    };

    // Helper Methods
    extractToken(req) {
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            return authHeader.substring(7);
        }
        
        // Check for token in cookies
        if (req.cookies && req.cookies.accessToken) {
            return req.cookies.accessToken;
        }
        
        return null;
    }

    getClientIP(req) {
        return req.headers['x-forwarded-for'] || 
               req.headers['x-real-ip'] || 
               req.connection.remoteAddress || 
               req.socket.remoteAddress || 
               (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
               req.ip;
    }
}

module.exports = new AuthMiddleware();
```

### Data Filtering Service

```javascript
// services/DataFilterService.js
class DataFilterService {
    constructor() {
        this.resourceSecurityService = new (require('./ResourceSecurityService'))();
        this.permissionService = new (require('./PermissionService'))();
        this.cache = new (require('./CacheService'))();
    }

    async filterInventoryItems(userId, items, options = {}) {
        const filteredItems = [];
        const userPermissions = await this.getUserPermissions(userId);
        const userContext = await this.getUserContext(userId);

        for (const item of items) {
            // Check basic read access
            const hasAccess = await this.resourceSecurityService.checkResourceAccess(
                userId, 'INVENTORY_ITEM', item.item_id, 'READ'
            );

            if (!hasAccess.allowed) {
                continue;
            }

            // Apply field-level filtering
            const filteredItem = await this.applyFieldLevelFiltering(
                userId, 'INVENTORY_ITEM', item, userPermissions
            );

            // Apply data masking for sensitive fields
            const maskedItem = await this.applySensitiveDataMasking(
                userId, 'INVENTORY_ITEM', filteredItem, userPermissions
            );

            filteredItems.push(maskedItem);
        }

        return filteredItems;
    }

    async filterOrders(userId, orders, options = {}) {
        const filteredOrders = [];
        const userContext = await this.getUserContext(userId);

        for (const order of orders) {
            const hasAccess = await this.resourceSecurityService.checkResourceAccess(
                userId, 'ORDER', order.order_id, 'READ'
            );

            if (!hasAccess.allowed) {
                continue;
            }

            // Filter order details based on user role and permissions
            const filteredOrder = { ...order };

            // Hide financial information from non-financial users
            if (!await this.hasPermission(userId, 'ORDER', 'VIEW_FINANCIAL_DETAILS')) {
                delete filteredOrder.unit_cost;
                delete filteredOrder.total_cost;
                delete filteredOrder.discount_amount;
                delete filteredOrder.tax_amount;
            }

            // Hide supplier details from unauthorized users
            if (!await this.hasPermission(userId, 'SUPPLIER', 'READ')) {
                delete filteredOrder.supplier_contact;
                delete filteredOrder.supplier_email;
                if (filteredOrder.Supplier) {
                    filteredOrder.Supplier = {
                        supplier_id: filteredOrder.Supplier.supplier_id,
                        company_name: filteredOrder.Supplier.company_name
                    };
                }
            }

            filteredOrders.push(filteredOrder);
        }

        return filteredOrders;
    }

    async filterUsers(userId, users, options = {}) {
        const filteredUsers = [];
        const requestingUserContext = await this.getUserContext(userId);

        for (const user of users) {
            // Users can always see their own data
            if (user.user_id === userId) {
                filteredUsers.push(user);
                continue;
            }

            // Check if user has permission to view other users
            if (!await this.hasPermission(userId, 'USER', 'READ')) {
                continue;
            }

            // Apply hierarchical filtering (managers can see subordinates)
            const canViewUser = await this.checkHierarchicalAccess(userId, user.user_id);
            if (!canViewUser) {
                continue;
            }

            // Apply field-level filtering for user data
            const filteredUser = { ...user };

            // Remove sensitive fields for non-admin users
            if (!await this.hasRole(userId, 'ADMIN')) {
                delete filteredUser.password_hash;
                delete filteredUser.mfa_secret;
                delete filteredUser.mfa_backup_codes;
                delete filteredUser.reset_token;
                delete filteredUser.verification_token;
            }

            // Remove PII for users without specific permissions
            if (!await this.hasPermission(userId, 'USER', 'VIEW_PII')) {
                filteredUser.email = this.maskEmail(filteredUser.email);
                delete filteredUser.phone;
                delete filteredUser.address;
            }

            filteredUsers.push(filteredUser);
        }

        return filteredUsers;
    }

    async applyFieldLevelFiltering(userId, resourceType, data, userPermissions) {
        const fieldPermissions = await this.getFieldPermissions(userId, resourceType);
        const filteredData = { ...data };

        // Remove fields user doesn't have permission to see
        for (const field in filteredData) {
            if (fieldPermissions.hiddenFields && fieldPermissions.hiddenFields.includes(field)) {
                delete filteredData[field];
            }
        }

        return filteredData;
    }

    async applySensitiveDataMasking(userId, resourceType, data, userPermissions) {
        const maskedData = { ...data };
        const maskingRules = await this.getMaskingRules(userId, resourceType);

        for (const rule of maskingRules) {
            if (maskedData[rule.field] !== undefined) {
                maskedData[rule.field] = this.applyMasking(maskedData[rule.field], rule.maskType);
            }
        }

        return maskedData;
    }

    async getFieldPermissions(userId, resourceType) {
        const cacheKey = `field_permissions:${userId}:${resourceType}`;
        const cached = await this.cache.redis.get(cacheKey);

        if