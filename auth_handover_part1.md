# Developer Handover Document: Authentication & Authorization Services

## Table of Contents
1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Database Schema](#database-schema)
4. [Authentication Logic Implementation](#authentication-logic-implementation)
5. [Authorization Logic Implementation](#authorization-logic-implementation)
6. [API Endpoints](#api-endpoints)
7. [Security Configuration](#security-configuration)
8. [Error Handling](#error-handling)
9. [Testing Strategy](#testing-strategy)
10. [Deployment Configuration](#deployment-configuration)
11. [Monitoring & Logging](#monitoring--logging)
12. [Troubleshooting Guide](#troubleshooting-guide)

## Overview

### Purpose
This document provides comprehensive technical specifications for implementing the Authentication & Authorization services for the Inventory Management System. It serves as a complete guide for developers taking over or maintaining this component.

### Technology Stack
- **Backend**: Node.js with Express.js framework
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT (JSON Web Tokens)
- **Password Hashing**: bcrypt
- **Rate Limiting**: express-rate-limit
- **2FA**: speakeasy (TOTP) + nodemailer/twilio
- **Session Storage**: Redis
- **Validation**: Joi or Yup

### Key Requirements
- Secure user authentication with JWT tokens
- Role-based access control (RBAC)
- Multi-factor authentication (MFA)
- Session management with Redis
- Account lockout protection
- Password reset flow
- Comprehensive audit logging

## System Architecture

### High-Level Flow
```
Client Request → Auth Middleware → Permission Check → Route Handler → Response
                     ↓
              JWT Validation → User Session → Role/Permission Lookup
```

### Components
1. **AuthService**: Core authentication logic
2. **AuthorizationService**: Permission and role management
3. **TokenService**: JWT token management
4. **SessionService**: Session tracking with Redis
5. **MFAService**: Multi-factor authentication handling
6. **PasswordService**: Password operations and validation

## Database Schema

### Required Tables

#### USERS Table
```sql
CREATE TABLE users (
    user_id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    is_email_verified BOOLEAN DEFAULT false,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    account_locked_until TIMESTAMP NULL,
    last_login TIMESTAMP,
    mfa_enabled BOOLEAN DEFAULT false,
    mfa_secret VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT REFERENCES users(user_id)
);
```

#### USER_ROLES Table
```sql
CREATE TABLE user_roles (
    role_id BIGSERIAL PRIMARY KEY,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### USER_ROLE_ASSIGNMENTS Table
```sql
CREATE TABLE user_role_assignments (
    assignment_id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(user_id),
    role_id BIGINT NOT NULL REFERENCES user_roles(role_id),
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by BIGINT REFERENCES users(user_id),
    UNIQUE(user_id, role_id)
);
```

#### PERMISSIONS Table
```sql
CREATE TABLE permissions (
    permission_id BIGSERIAL PRIMARY KEY,
    permission_name VARCHAR(100) UNIQUE NOT NULL,
    resource VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### ROLE_PERMISSIONS Table
```sql
CREATE TABLE role_permissions (
    role_permission_id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL REFERENCES user_roles(role_id),
    permission_id BIGINT NOT NULL REFERENCES permissions(permission_id),
    granted BOOLEAN DEFAULT true,
    UNIQUE(role_id, permission_id)
);
```

#### USER_SESSIONS Table
```sql
CREATE TABLE user_sessions (
    session_id VARCHAR(255) PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(user_id),
    refresh_token VARCHAR(500) NOT NULL,
    device_info JSONB,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### AUDIT_LOGS Table
```sql
CREATE TABLE audit_logs (
    log_id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(user_id),
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100),
    resource_id VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    details JSONB,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Database Indexes
```sql
-- Performance indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_active ON user_sessions(is_active);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);
```

## Authentication Logic Implementation

### 1. User Login Validation

#### Service Implementation
```javascript
// services/AuthService.js
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const speakeasy = require('speakeasy');

class AuthService {
    async validateLogin(email, password, mfaToken = null) {
        try {
            // Step 1: Find user by email
            const user = await User.findOne({ 
                where: { email, is_active: true } 
            });
            
            if (!user) {
                await this.logAuditEvent(null, 'LOGIN_FAILED', 'USER', null, {
                    reason: 'User not found',
                    email: email
                });
                throw new Error('Invalid credentials');
            }

            // Step 2: Check account lockout
            if (await this.isAccountLocked(user)) {
                await this.logAuditEvent(user.user_id, 'LOGIN_BLOCKED', 'USER', user.user_id, {
                    reason: 'Account locked'
                });
                throw new Error('Account is temporarily locked');
            }

            // Step 3: Validate password
            const isValidPassword = await bcrypt.compare(password, user.password_hash);
            
            if (!isValidPassword) {
                await this.handleFailedLogin(user);
                throw new Error('Invalid credentials');
            }

            // Step 4: MFA validation if enabled
            if (user.mfa_enabled) {
                if (!mfaToken) {
                    return { requiresMFA: true, tempToken: this.generateTempToken(user) };
                }
                
                const isValidMFA = speakeasy.totp.verify({
                    secret: user.mfa_secret,
                    token: mfaToken,
                    window: 2
                });
                
                if (!isValidMFA) {
                    await this.logAuditEvent(user.user_id, 'MFA_FAILED', 'USER', user.user_id);
                    throw new Error('Invalid MFA token');
                }
            }

            // Step 5: Reset failed attempts and update last login
            await this.handleSuccessfulLogin(user);

            // Step 6: Generate tokens
            const tokens = await this.generateTokens(user);
            
            await this.logAuditEvent(user.user_id, 'LOGIN_SUCCESS', 'USER', user.user_id);
            
            return {
                user: this.sanitizeUser(user),
                tokens
            };
            
        } catch (error) {
            throw error;
        }
    }

    async handleFailedLogin(user) {
        const failedAttempts = user.failed_login_attempts + 1;
        const maxAttempts = process.env.MAX_LOGIN_ATTEMPTS || 5;
        const lockoutDuration = process.env.LOCKOUT_DURATION_MINUTES || 30;

        const updateData = {
            failed_login_attempts: failedAttempts
        };

        if (failedAttempts >= maxAttempts) {
            updateData.account_locked_until = new Date(
                Date.now() + lockoutDuration * 60 * 1000
            );
        }

        await User.update(updateData, {
            where: { user_id: user.user_id }
        });

        await this.logAuditEvent(user.user_id, 'LOGIN_FAILED', 'USER', user.user_id, {
            failed_attempts: failedAttempts,
            locked: failedAttempts >= maxAttempts
        });
    }

    async handleSuccessfulLogin(user) {
        await User.update({
            failed_login_attempts: 0,
            account_locked_until: null,
            last_login: new Date()
        }, {
            where: { user_id: user.user_id }
        });
    }

    async isAccountLocked(user) {
        if (!user.account_locked_until) return false;
        
        if (new Date() > user.account_locked_until) {
            // Auto-unlock expired lockouts
            await User.update({
                account_locked_until: null,
                failed_login_attempts: 0
            }, {
                where: { user_id: user.user_id }
            });
            return false;
        }
        
        return true;
    }
}
```

### 2. JWT Token Generation

#### Token Service Implementation
```javascript
// services/TokenService.js
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const Redis = require('redis');

class TokenService {
    constructor() {
        this.redis = Redis.createClient(process.env.REDIS_URL);
        this.accessTokenExpiry = process.env.ACCESS_TOKEN_EXPIRY || '15m';
        this.refreshTokenExpiry = process.env.REFRESH_TOKEN_EXPIRY || '7d';
    }

    async generateTokens(user, deviceInfo = {}) {
        const payload = {
            userId: user.user_id,
            email: user.email,
            roles: await this.getUserRoles(user.user_id),
            sessionId: crypto.randomUUID()
        };

        // Generate access token
        const accessToken = jwt.sign(payload, process.env.JWT_SECRET, {
            expiresIn: this.accessTokenExpiry,
            issuer: process.env.JWT_ISSUER,
            audience: process.env.JWT_AUDIENCE
        });

        // Generate refresh token
        const refreshToken = jwt.sign(
            { userId: user.user_id, sessionId: payload.sessionId },
            process.env.JWT_REFRESH_SECRET,
            { expiresIn: this.refreshTokenExpiry }
        );

        // Store session in database
        await this.storeSession({
            sessionId: payload.sessionId,
            userId: user.user_id,
            refreshToken,
            deviceInfo,
            accessToken
        });

        // Cache user permissions in Redis
        await this.cacheUserPermissions(user.user_id, payload.roles);

        return {
            accessToken,
            refreshToken,
            expiresIn: this.parseExpiry(this.accessTokenExpiry),
            tokenType: 'Bearer'
        };
    }

    async refreshAccessToken(refreshToken) {
        try {
            // Verify refresh token
            const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
            
            // Check if session exists and is active
            const session = await UserSession.findOne({
                where: {
                    session_id: decoded.sessionId,
                    user_id: decoded.userId,
                    is_active: true,
                    expires_at: { [Op.gt]: new Date() }
                }
            });

            if (!session) {
                throw new Error('Invalid refresh token');
            }

            // Get fresh user data
            const user = await User.findByPk(decoded.userId, {
                include: [{ model: UserRole, include: [Role] }]
            });

            if (!user || !user.is_active) {
                throw new Error('User not found or inactive');
            }

            // Generate new access token
            const payload = {
                userId: user.user_id,
                email: user.email,
                roles: await this.getUserRoles(user.user_id),
                sessionId: decoded.sessionId
            };

            const accessToken = jwt.sign(payload, process.env.JWT_SECRET, {
                expiresIn: this.accessTokenExpiry,
                issuer: process.env.JWT_ISSUER,
                audience: process.env.JWT_AUDIENCE
            });

            // Update session last accessed
            await UserSession.update(
                { last_accessed: new Date() },
                { where: { session_id: decoded.sessionId } }
            );

            return {
                accessToken,
                expiresIn: this.parseExpiry(this.accessTokenExpiry),
                tokenType: 'Bearer'
            };

        } catch (error) {
            throw new Error('Invalid refresh token');
        }
    }

    async revokeToken(sessionId, userId) {
        // Deactivate session
        await UserSession.update(
            { is_active: false },
            { where: { session_id: sessionId, user_id: userId } }
        );

        // Remove from Redis cache
        await this.redis.del(`user_permissions:${userId}`);
        await this.redis.del(`user_session:${sessionId}`);
    }

    async cacheUserPermissions(userId, roles) {
        const permissions = await this.getUserPermissions(roles);
        await this.redis.setex(
            `user_permissions:${userId}`,
            3600, // 1 hour
            JSON.stringify(permissions)
        );
    }
}
```

### 3. Multi-Factor Authentication

#### MFA Service Implementation
```javascript
// services/MFAService.js
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const nodemailer = require('nodemailer');

class MFAService {
    async setupTOTP(userId) {
        const user = await User.findByPk(userId);
        if (!user) throw new Error('User not found');

        // Generate secret
        const secret = speakeasy.generateSecret({
            name: `${user.email}`,
            issuer: process.env.APP_NAME || 'Inventory Management'
        });

        // Store temporary secret (not activated until verified)
        await User.update(
            { mfa_secret: secret.base32 },
            { where: { user_id: userId } }
        );

        // Generate QR code
        const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

        return {
            secret: secret.base32,
            qrCode: qrCodeUrl,
            manualEntryKey: secret.base32
        };
    }

    async verifyAndEnableTOTP(userId, token) {
        const user = await User.findByPk(userId);
        if (!user || !user.mfa_secret) {
            throw new Error('MFA setup not initialized');
        }

        const verified = speakeasy.totp.verify({
            secret: user.mfa_secret,
            token: token,
            window: 2
        });

        if (!verified) {
            throw new Error('Invalid TOTP token');
        }

        // Enable MFA
        await User.update(
            { mfa_enabled: true },
            { where: { user_id: userId } }
        );

        await this.logAuditEvent(userId, 'MFA_ENABLED', 'USER', userId);

        return { success: true, message: 'MFA enabled successfully' };
    }

    async disableMFA(userId, currentPassword) {
        const user = await User.findByPk(userId);
        if (!user) throw new Error('User not found');

        // Verify current password
        const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);
        if (!isValidPassword) {
            throw new Error('Invalid current password');
        }

        await User.update({
            mfa_enabled: false,
            mfa_secret: null
        }, {
            where: { user_id: userId }
        });

        await this.logAuditEvent(userId, 'MFA_DISABLED', 'USER', userId);

        return { success: true, message: 'MFA disabled successfully' };
    }

    async sendEmailOTP(userId, email) {
        const otp = Math.floor(100000 + Math.random() * 900000).toString();
        const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

        // Store OTP in Redis
        await this.redis.setex(`email_otp:${userId}`, 600, JSON.stringify({
            otp,
            email,
            expiresAt
        }));

        // Send email
        await this.sendOTPEmail(email, otp);

        return { success: true, message: 'OTP sent to email' };
    }

    async verifyEmailOTP(userId, otp) {
        const stored = await this.redis.get(`email_otp:${userId}`);
        if (!stored) {
            throw new Error('OTP expired or not found');
        }

        const { otp: storedOTP, expiresAt } = JSON.parse(stored);
        
        if (new Date() > new Date(expiresAt)) {
            await this.redis.del(`email_otp:${userId}`);
            throw new Error('OTP expired');
        }

        if (otp !== storedOTP) {
            throw new Error('Invalid OTP');
        }

        // Clear OTP
        await this.redis.del(`email_otp:${userId}`);

        return { success: true, message: 'OTP verified successfully' };
    }
}
```

### 4. Password Reset Flow

#### Password Service Implementation
```javascript
// services/PasswordService.js
const bcrypt = require('bcrypt');
const crypto = require('crypto');

class PasswordService {
    async initiatePasswordReset(email) {
        const user = await User.findOne({ where: { email, is_active: true } });
        
        if (!user) {
            // Don't reveal if email exists - security best practice
            return { success: true, message: 'Password reset instructions sent if email exists' };
        }

        // Generate secure reset token
        const resetToken = crypto.randomBytes(32).toString('hex');
        const resetTokenHash = crypto.createHash('sha256').update(resetToken).digest('hex');
        const resetExpires = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

        // Store hashed token
        await User.update({
            password_reset_token: resetTokenHash,
            password_reset_expires: resetExpires
        }, {
            where: { user_id: user.user_id }
        });

        // Send reset email
        await this.sendPasswordResetEmail(user.email, resetToken);

        await this.logAuditEvent(user.user_id, 'PASSWORD_RESET_REQUESTED', 'USER', user.user_id);

        return { success: true, message: 'Password reset instructions sent if email exists' };
    }

    async resetPassword(token, newPassword) {
        // Hash the provided token to compare with stored hash
        const resetTokenHash = crypto.createHash('sha256').update(token).digest('hex');

        const user = await User.findOne({
            where: {
                password_reset_token: resetTokenHash,
                password_reset_expires: { [Op.gt]: new Date() },
                is_active: true
            }
        });

        if (!user) {
            throw new Error('Invalid or expired reset token');
        }

        // Validate new password
        await this.validatePasswordStrength(newPassword);

        // Hash new password
        const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
        const passwordHash = await bcrypt.hash(newPassword, saltRounds);

        // Update password and clear reset token
        await User.update({
            password_hash: passwordHash,
            password_reset_token: null,
            password_reset_expires: null,
            failed_login_attempts: 0, // Reset failed attempts
            account_locked_until: null // Unlock account if locked
        }, {
            where: { user_id: user.user_id }
        });

        // Revoke all existing sessions
        await this.revokeAllUserSessions(user.user_id);

        await this.logAuditEvent(user.user_id, 'PASSWORD_RESET_COMPLETED', 'USER', user.user_id);

        return { success: true, message: 'Password reset successfully' };
    }

    async changePassword(userId, currentPassword, newPassword) {
        const user = await User.findByPk(userId);
        if (!user) throw new Error('User not found');

        // Verify current password
        const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);
        if (!isValidPassword) {
            throw new Error('Current password is incorrect');
        }

        // Validate new password
        await this.validatePasswordStrength(newPassword);

        // Check if new password is different from current
        const isSamePassword = await bcrypt.compare(newPassword, user.password_hash);
        if (isSamePassword) {
            throw new Error('New password must be different from current password');
        }

        // Hash new password
        const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
        const passwordHash = await bcrypt.hash(newPassword, saltRounds);

        await User.update(
            { password_hash: passwordHash },
            { where: { user_id: userId } }
        );

        await this.logAuditEvent(userId, 'PASSWORD_CHANGED', 'USER', userId);

        return { success: true, message: 'Password changed successfully' };
    }

    async validatePasswordStrength(password) {
        const minLength = parseInt(process.env.PASSWORD_MIN_LENGTH) || 8;
        const requireUppercase = process.env.PASSWORD_REQUIRE_UPPERCASE === 'true';
        const requireLowercase = process.env.PASSWORD_REQUIRE_LOWERCASE === 'true';
        const requireNumbers = process.env.PASSWORD_REQUIRE_NUMBERS === 'true';
        const requireSpecialChars = process.env.PASSWORD_REQUIRE_SPECIAL === 'true';

        const errors = [];

        if (password.length < minLength) {
            errors.push(`Password must be at least ${minLength} characters long`);
        }

        if (requireUppercase && !/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }

        if (requireLowercase && !/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }

        if (requireNumbers && !/\d/.test(password)) {
            errors.push('Password must contain at least one number');
        }

        if (requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }

        if (errors.length > 0) {
            throw new Error(errors.join('. '));
        }
    }
}
```

## Authorization Logic Implementation

### 1. Role-Based Access Control (RBAC)

#### Authorization Service Implementation
```javascript
// services/AuthorizationService.js
class AuthorizationService {
    async getUserPermissions(userId) {
        // Check Redis cache first
        const cached = await this.redis.get(`user_permissions:${userId}`);
        if (cached) {
            return JSON.parse(cached);
        }

        // Query database
        const userRoles = await UserRoleAssignment.findAll({
            where: { user_id: userId },
            include: [{
                model: UserRole,
                include: [{
                    model: RolePermission,
                    include: [Permission]
                }]
            }]
        });

        const permissions = new Set();
        
        userRoles.forEach(assignment => {
            assignment.UserRole.RolePermissions.forEach(rolePermission => {
                if (rolePermission.granted) {
                    const permission = rolePermission.Permission;
                    permissions.add(`${permission.resource}:${permission.action}`);
                }
            });
        });

        const permissionArray = Array.from(permissions);
        
        // Cache for 1 hour
        await this.redis.setex(
            `user_permissions:${userId}`,
            3600,
            JSON.stringify(permissionArray)
        );

        return permissionArray;
    }

    async hasPermission(userId, resource, action) {
        const permissions = await this.getUserPermissions(userId);
        const requiredPermission = `${resource}:${action}`;
        
        return permissions.includes(requiredPermission) || 
               permissions.includes(`${resource}:*`) ||
               permissions.includes('*:*');
    }

    async hasAnyPermission(userId, permissionList) {
        const userPermissions = await this.getUserPermissions(userId);
        
        return permissionList.some(permission => {
            const [resource, action] = permission.split(':');
            return userPermissions.includes(permission) ||
                   userPermissions.includes(`${resource}:*`) ||
                   userPermissions.includes('*:*');
        });
    }

    async hasRole(userId, roleName) {
        const userRoles = await UserRoleAssignment.findAll({
            where: { user_id: userId },
            include: [{
                model: UserRole,
                where: { role_name: roleName, is_active: true }
            }]
        });

        return userRoles.length > 0;
    }

    async assignRole(userId, roleId, assignedBy) {
        // Check if role exists and is active
        const role = await UserRole.findOne({
            where: { role_id: roleId, is_active: true }
        });

        if (!role) {
            throw new Error('Role not found or inactive');
        }

        // Check if user already has this role
        const existing = await UserRoleAssignment.findOne({
            where: { user_id: userId, role_id: roleId }
        });

        if (existing) {
            throw new Error('User already has this role');
        }

        // Assign role
        await UserRoleAssignment.create({
            user_id: userId,
            role_id: roleId,
            assigned_by: assignedBy
        });

        // Clear cached permissions
        await this.redis.del(`user_permissions:${userId}`);

        await this.logAuditEvent(assignedBy, 'ROLE_ASSIGNED', 'USER', userId, {
            role_id: roleId,
            role_name: role.role_name
        });

        return { success: true, message: 'Role assigned successfully' };
    }

    async revokeRole(userId, roleId, revokedBy) {
        const assignment = await UserRoleAssignment.findOne({
            where: { user_id: userId, role_id: roleId }
        });

        if (!assignment) {
            throw new Error('User does not have this role');
        }

        await assignment.destroy();

        // Clear cached permissions
        await this.redis.del(`user_permissions:${userId}`);

        const role = await UserRole.findByPk(roleId);
        await this.logAuditEvent(revokedBy, 'ROLE_REVOKED', 'USER', userId, {
            role_id: roleId,
            role_name: role?.role_name
        });

        return { success: true, message: 'Role revoked successfully' };
    }
}
```

### 2. API Route Protection Middleware

#### Authentication Middleware
```javascript
// middleware/authMiddleware.js
const jwt = require('jsonwebtoken');

const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

        if (!token) {
            return res.status(401).json({
                error: 'Access token required',
                code: 'TOKEN_MISSING'
            });
        }

        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        // Check if session is still active
        const session = await UserSession.findOne({
            where: {
                session_id: decoded.sessionId,
                user_id: decoded.userId,
                is_active: true,
                expires_at: { [Op.gt]: new Date() }
            }
        });

        if (!session) {
            return res.status(401).json({
                error: 'Session expired or invalid',
                code: 'SESSION_INVALID'
            });
        }

        // Check if user is still active
        const user = await User.findOne({
            where: { user_id: decoded.userId, is_active: true }
        });

        if (!user) {
            return res.status(401).json({
                error: 'User account inactive',
                code: 'USER_INACTIVE'
            });
        }

        // Update session last accessed
        await UserSession.update(
            { last_accessed: new Date() },
            { where: { session_id: decoded.sessionId } }
        );

        // Attach user info to request
        req.user = {
            userId: decoded.userId,
            email: decoded.email,
            sessionId: decoded.sessionId,
            roles: decoded.roles
        };

        next();
    } catch (error) {
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                error: 'Token expired',
                code: 'TOKEN_EXPIRED'
            });
        } else if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                error: 'Invalid token',
                code: 'TOKEN_INVALID'
            });
        }

        console.error('Authentication error:', error);
        return res.status(500).json({
            error: 'Authentication failed',
            code: 'AUTH_ERROR'
        });
    }
};

module.exports = authenticateToken;
```

#### Permission Middleware
```javascript
// middleware/permissionMiddleware.js
const requirePermission = (resource, action) => {
    return async (req, res, next) => {
        try {
            const { userId } = req.user;
            
            const authService = new AuthorizationService();
            const hasPermission = await authService.hasPermission(userId, resource, action);

            if (!hasPerm