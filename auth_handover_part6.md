# Developer Handover Document: Authentication & Authorization Services (Part 6)

*Continuation from Part 5...*

```javascript
        if (cached) {
            return JSON.parse(cached);
        }

        const fieldPermissions = await FieldPermission.findAll({
            where: { 
                user_id: userId, 
                resource_type: resourceType,
                is_active: true 
            }
        });

        const result = {
            hiddenFields: [],
            readOnlyFields: [],
            maskedFields: []
        };

        fieldPermissions.forEach(fp => {
            const permissions = JSON.parse(fp.permissions);
            if (permissions.hidden) result.hiddenFields.push(fp.field_name);
            if (permissions.readOnly) result.readOnlyFields.push(fp.field_name);
            if (permissions.masked) result.maskedFields.push(fp.field_name);
        });

        // Cache for 10 minutes
        await this.cache.redis.setex(cacheKey, 600, JSON.stringify(result));
        
        return result;
    }

    async getMaskingRules(userId, resourceType) {
        const cacheKey = `masking_rules:${userId}:${resourceType}`;
        const cached = await this.cache.redis.get(cacheKey);

        if (cached) {
            return JSON.parse(cached);
        }

        const rules = await DataMaskingRule.findAll({
            where: { 
                resource_type: resourceType,
                is_active: true
            },
            include: [
                {
                    model: UserRole,
                    where: { user_id: userId },
                    through: { attributes: [] }
                }
            ]
        });

        const result = rules.map(rule => ({
            field: rule.field_name,
            maskType: rule.mask_type,
            maskChar: rule.mask_character || '*',
            preserveLength: rule.preserve_length,
            showFirst: rule.show_first_chars,
            showLast: rule.show_last_chars
        }));

        // Cache for 30 minutes
        await this.cache.redis.setex(cacheKey, 1800, JSON.stringify(result));
        
        return result;
    }

    applyMasking(value, maskType, options = {}) {
        if (!value || typeof value !== 'string') return value;

        const { maskChar = '*', preserveLength = true, showFirst = 0, showLast = 0 } = options;

        switch (maskType) {
            case 'FULL':
                return preserveLength ? maskChar.repeat(value.length) : '***';
            
            case 'PARTIAL':
                if (value.length <= showFirst + showLast) {
                    return maskChar.repeat(value.length);
                }
                const first = value.substring(0, showFirst);
                const last = value.substring(value.length - showLast);
                const middle = maskChar.repeat(value.length - showFirst - showLast);
                return first + middle + last;
            
            case 'EMAIL':
                return this.maskEmail(value);
            
            case 'PHONE':
                return this.maskPhone(value);
            
            case 'FINANCIAL':
                return this.maskFinancial(value);
            
            default:
                return value;
        }
    }

    maskEmail(email) {
        if (!email || !email.includes('@')) return email;
        
        const [local, domain] = email.split('@');
        const maskedLocal = local.length > 2 
            ? local[0] + '*'.repeat(local.length - 2) + local[local.length - 1]
            : '*'.repeat(local.length);
        
        return `${maskedLocal}@${domain}`;
    }

    maskPhone(phone) {
        if (!phone) return phone;
        
        const cleaned = phone.replace(/\D/g, '');
        if (cleaned.length < 4) return '*'.repeat(cleaned.length);
        
        return '*'.repeat(cleaned.length - 4) + cleaned.slice(-4);
    }

    maskFinancial(amount) {
        if (!amount) return amount;
        
        const strAmount = amount.toString();
        return '*'.repeat(strAmount.length - 2) + strAmount.slice(-2);
    }

    async checkHierarchicalAccess(requestingUserId, targetUserId) {
        const cacheKey = `hierarchical_access:${requestingUserId}:${targetUserId}`;
        const cached = await this.cache.redis.get(cacheKey);

        if (cached) {
            return JSON.parse(cached);
        }

        const requestingUser = await User.findByPk(requestingUserId, {
            include: [
                { model: Department },
                { model: UserRole, include: [Role] }
            ]
        });

        const targetUser = await User.findByPk(targetUserId, {
            include: [{ model: Department }]
        });

        let hasAccess = false;

        // Same department access
        if (requestingUser.department_id === targetUser.department_id) {
            const isManager = requestingUser.UserRoles.some(ur => 
                ['MANAGER', 'DEPARTMENT_HEAD'].includes(ur.Role.role_name)
            );
            if (isManager) hasAccess = true;
        }

        // Cross-department access for admins
        const isAdmin = requestingUser.UserRoles.some(ur => 
            ur.Role.role_name === 'ADMIN'
        );
        if (isAdmin) hasAccess = true;

        // HR can access all users
        const isHR = requestingUser.UserRoles.some(ur => 
            ur.Role.role_name === 'HR'
        );
        if (isHR) hasAccess = true;

        // Cache for 5 minutes
        await this.cache.redis.setex(cacheKey, 300, JSON.stringify(hasAccess));
        
        return hasAccess;
    }

    async getUserPermissions(userId) {
        return await this.permissionService.getUserPermissions(userId);
    }

    async getUserContext(userId) {
        return await this.resourceSecurityService.getUserContext(userId);
    }

    async hasPermission(userId, resource, action) {
        return await this.permissionService.hasPermission(userId, resource, action);
    }

    async hasRole(userId, roleName) {
        const user = await User.findByPk(userId, {
            include: [{ model: UserRole, include: [Role] }]
        });
        
        return user.UserRoles.some(ur => ur.Role.role_name === roleName);
    }
}

module.exports = DataFilterService;
```

### Security Audit Service

```javascript
// services/SecurityAuditService.js
class SecurityAuditService {
    constructor() {
        this.cache = new (require('./CacheService'))();
        this.alertService = new (require('./AlertService'))();
    }

    async performSecurityAudit(userId, auditType = 'COMPREHENSIVE') {
        const auditResults = {
            auditId: require('uuid').v4(),
            userId: userId,
            auditType: auditType,
            timestamp: new Date(),
            findings: [],
            riskScore: 0,
            recommendations: []
        };

        try {
            switch (auditType) {
                case 'USER_PERMISSIONS':
                    await this.auditUserPermissions(userId, auditResults);
                    break;
                case 'ACCESS_PATTERNS':
                    await this.auditAccessPatterns(userId, auditResults);
                    break;
                case 'SECURITY_VIOLATIONS':
                    await this.auditSecurityViolations(userId, auditResults);
                    break;
                case 'COMPREHENSIVE':
                    await this.auditUserPermissions(userId, auditResults);
                    await this.auditAccessPatterns(userId, auditResults);
                    await this.auditSecurityViolations(userId, auditResults);
                    break;
            }

            // Calculate overall risk score
            auditResults.riskScore = this.calculateRiskScore(auditResults.findings);
            
            // Generate recommendations
            auditResults.recommendations = this.generateRecommendations(auditResults.findings);

            // Store audit results
            await this.storeAuditResults(auditResults);

            // Send alerts for high-risk findings
            if (auditResults.riskScore > 70) {
                await this.alertService.sendSecurityAlert('HIGH_RISK_AUDIT', auditResults);
            }

            return auditResults;
        } catch (error) {
            console.error('Security audit error:', error);
            throw error;
        }
    }

    async auditUserPermissions(userId, auditResults) {
        const user = await User.findByPk(userId, {
            include: [
                { model: UserRole, include: [Role] },
                { model: Department }
            ]
        });

        const findings = [];

        // Check for excessive permissions
        const allPermissions = await this.getAllUserPermissions(userId);
        if (allPermissions.length > 50) {
            findings.push({
                type: 'EXCESSIVE_PERMISSIONS',
                severity: 'MEDIUM',
                description: `User has ${allPermissions.length} permissions, which may be excessive`,
                data: { permissionCount: allPermissions.length }
            });
        }

        // Check for admin privileges
        const hasAdminRole = user.UserRoles.some(ur => ur.Role.role_name === 'ADMIN');
        if (hasAdminRole) {
            findings.push({
                type: 'ADMIN_PRIVILEGES',
                severity: 'HIGH',
                description: 'User has administrative privileges',
                data: { roles: user.UserRoles.map(ur => ur.Role.role_name) }
            });
        }

        // Check for role conflicts
        const roleConflicts = await this.checkRoleConflicts(user.UserRoles);
        if (roleConflicts.length > 0) {
            findings.push({
                type: 'ROLE_CONFLICTS',
                severity: 'HIGH',
                description: 'User has conflicting roles assigned',
                data: { conflicts: roleConflicts }
            });
        }

        // Check for unused permissions
        const unusedPermissions = await this.findUnusedPermissions(userId);
        if (unusedPermissions.length > 10) {
            findings.push({
                type: 'UNUSED_PERMISSIONS',
                severity: 'LOW',
                description: `User has ${unusedPermissions.length} unused permissions`,
                data: { unusedPermissions: unusedPermissions }
            });
        }

        // Check for expired dynamic permissions
        const expiredPermissions = await this.findExpiredDynamicPermissions(userId);
        if (expiredPermissions.length > 0) {
            findings.push({
                type: 'EXPIRED_PERMISSIONS',
                severity: 'MEDIUM',
                description: 'User has expired dynamic permissions that should be cleaned up',
                data: { expiredPermissions: expiredPermissions }
            });
        }

        auditResults.findings.push(...findings);
    }

    async auditAccessPatterns(userId, auditResults) {
        const findings = [];
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

        // Get user's access logs
        const accessLogs = await AuditLog.findAll({
            where: {
                user_id: userId,
                created_at: { [Op.gte]: thirtyDaysAgo }
            },
            order: [['created_at', 'DESC']]
        });

        // Check for unusual access times
        const offHoursAccess = accessLogs.filter(log => {
            const hour = log.created_at.getHours();
            return hour < 6 || hour > 22; // Outside 6 AM - 10 PM
        });

        if (offHoursAccess.length > 10) {
            findings.push({
                type: 'OFF_HOURS_ACCESS',
                severity: 'MEDIUM',
                description: `User has ${offHoursAccess.length} access events outside normal hours`,
                data: { offHoursCount: offHoursAccess.length }
            });
        }

        // Check for multiple IP addresses
        const uniqueIPs = [...new Set(accessLogs.map(log => log.ip_address))];
        if (uniqueIPs.length > 5) {
            findings.push({
                type: 'MULTIPLE_IP_ADDRESSES',
                severity: 'HIGH',
                description: `User accessed from ${uniqueIPs.length} different IP addresses`,
                data: { ipAddresses: uniqueIPs }
            });
        }

        // Check for failed login attempts
        const failedLogins = accessLogs.filter(log => 
            log.event_type === 'LOGIN_FAILED'
        );

        if (failedLogins.length > 10) {
            findings.push({
                type: 'EXCESSIVE_FAILED_LOGINS',
                severity: 'HIGH',
                description: `User has ${failedLogins.length} failed login attempts`,
                data: { failedLoginCount: failedLogins.length }
            });
        }

        // Check for data export activities
        const dataExports = accessLogs.filter(log => 
            log.action && log.action.includes('EXPORT')
        );

        if (dataExports.length > 20) {
            findings.push({
                type: 'EXCESSIVE_DATA_EXPORTS',
                severity: 'HIGH',
                description: `User has performed ${dataExports.length} data export operations`,
                data: { exportCount: dataExports.length }
            });
        }

        // Check for privilege escalation attempts
        const privilegeEscalation = accessLogs.filter(log => 
            log.event_type === 'PERMISSION_DENIED' && 
            log.details && log.details.includes('ADMIN')
        );

        if (privilegeEscalation.length > 0) {
            findings.push({
                type: 'PRIVILEGE_ESCALATION_ATTEMPTS',
                severity: 'CRITICAL',
                description: 'User attempted to access administrative functions',
                data: { attempts: privilegeEscalation.length }
            });
        }

        auditResults.findings.push(...findings);
    }

    async auditSecurityViolations(userId, auditResults) {
        const findings = [];
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

        // Check for security events
        const securityEvents = await SecurityEvent.findAll({
            where: {
                user_id: userId,
                created_at: { [Op.gte]: thirtyDaysAgo }
            }
        });

        // Group by event type
        const eventTypes = {};
        securityEvents.forEach(event => {
            if (!eventTypes[event.event_type]) {
                eventTypes[event.event_type] = 0;
            }
            eventTypes[event.event_type]++;
        });

        // Check for suspicious activities
        if (eventTypes['SUSPICIOUS_LOGIN'] > 0) {
            findings.push({
                type: 'SUSPICIOUS_LOGIN_ACTIVITY',
                severity: 'HIGH',
                description: `User has ${eventTypes['SUSPICIOUS_LOGIN']} suspicious login events`,
                data: { count: eventTypes['SUSPICIOUS_LOGIN'] }
            });
        }

        if (eventTypes['RATE_LIMIT_EXCEEDED'] > 5) {
            findings.push({
                type: 'RATE_LIMIT_VIOLATIONS',
                severity: 'MEDIUM',
                description: `User exceeded rate limits ${eventTypes['RATE_LIMIT_EXCEEDED']} times`,
                data: { count: eventTypes['RATE_LIMIT_EXCEEDED'] }
            });
        }

        if (eventTypes['IP_BLOCKED'] > 0) {
            findings.push({
                type: 'IP_BLOCKING_EVENTS',
                severity: 'HIGH',
                description: `User's IP was blocked ${eventTypes['IP_BLOCKED']} times`,
                data: { count: eventTypes['IP_BLOCKED'] }
            });
        }

        // Check for password-related issues
        const user = await User.findByPk(userId);
        const passwordAge = Math.floor((Date.now() - user.password_changed_at.getTime()) / (1000 * 60 * 60 * 24));
        
        if (passwordAge > 90) {
            findings.push({
                type: 'OLD_PASSWORD',
                severity: 'MEDIUM',
                description: `User's password is ${passwordAge} days old`,
                data: { passwordAge: passwordAge }
            });
        }

        // Check MFA status
        if (!user.mfa_enabled) {
            findings.push({
                type: 'MFA_NOT_ENABLED',
                severity: 'HIGH',
                description: 'Multi-factor authentication is not enabled',
                data: { mfaEnabled: false }
            });
        }

        // Check for account lockouts
        if (user.failed_login_attempts > 0) {
            findings.push({
                type: 'FAILED_LOGIN_ATTEMPTS',
                severity: 'MEDIUM',
                description: `User has ${user.failed_login_attempts} recent failed login attempts`,
                data: { failedAttempts: user.failed_login_attempts }
            });
        }

        auditResults.findings.push(...findings);
    }

    async getAllUserPermissions(userId) {
        const permissionService = new (require('./PermissionService'))();
        return await permissionService.getUserPermissions(userId);
    }

    async checkRoleConflicts(userRoles) {
        const conflicts = [];
        const roleNames = userRoles.map(ur => ur.Role.role_name);
        
        // Define conflicting role pairs
        const conflictingRoles = [
            ['ADMIN', 'GUEST'],
            ['MANAGER', 'INTERN'],
            ['FINANCIAL_CONTROLLER', 'BASIC_USER']
        ];

        for (const [role1, role2] of conflictingRoles) {
            if (roleNames.includes(role1) && roleNames.includes(role2)) {
                conflicts.push({ role1, role2 });
            }
        }

        return conflicts;
    }

    async findUnusedPermissions(userId) {
        const allPermissions = await this.getAllUserPermissions(userId);
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

        const usedPermissions = await AuditLog.findAll({
            where: {
                user_id: userId,
                created_at: { [Op.gte]: thirtyDaysAgo },
                action: { [Op.not]: null }
            },
            attributes: ['action'],
            group: ['action']
        });

        const usedActions = usedPermissions.map(log => log.action);
        
        return allPermissions.filter(permission => 
            !usedActions.some(action => 
                action.includes(permission.resource) && action.includes(permission.action)
            )
        );
    }

    async findExpiredDynamicPermissions(userId) {
        return await DynamicPermission.findAll({
            where: {
                user_id: userId,
                expires_at: { [Op.lt]: new Date() },
                is_active: true
            }
        });
    }

    calculateRiskScore(findings) {
        let score = 0;
        
        findings.forEach(finding => {
            switch (finding.severity) {
                case 'CRITICAL':
                    score += 25;
                    break;
                case 'HIGH':
                    score += 15;
                    break;
                case 'MEDIUM':
                    score += 8;
                    break;
                case 'LOW':
                    score += 3;
                    break;
            }
        });

        return Math.min(score, 100); // Cap at 100
    }

    generateRecommendations(findings) {
        const recommendations = [];
        
        findings.forEach(finding => {
            switch (finding.type) {
                case 'EXCESSIVE_PERMISSIONS':
                    recommendations.push({
                        priority: 'MEDIUM',
                        action: 'Review and remove unnecessary permissions',
                        description: 'Conduct a permission audit and remove unused or excessive permissions'
                    });
                    break;
                
                case 'ADMIN_PRIVILEGES':
                    recommendations.push({
                        priority: 'HIGH',
                        action: 'Review administrative access',
                        description: 'Verify that administrative privileges are necessary and justified'
                    });
                    break;
                
                case 'MFA_NOT_ENABLED':
                    recommendations.push({
                        priority: 'HIGH',
                        action: 'Enable multi-factor authentication',
                        description: 'Implement MFA to enhance account security'
                    });
                    break;
                
                case 'OLD_PASSWORD':
                    recommendations.push({
                        priority: 'MEDIUM',
                        action: 'Force password change',
                        description: 'Require user to update their password'
                    });
                    break;
                
                case 'PRIVILEGE_ESCALATION_ATTEMPTS':
                    recommendations.push({
                        priority: 'CRITICAL',
                        action: 'Immediate security review',
                        description: 'Investigate privilege escalation attempts and consider account restrictions'
                    });
                    break;
            }
        });

        return recommendations;
    }

    async storeAuditResults(auditResults) {
        await SecurityAudit.create({
            audit_id: auditResults.auditId,
            user_id: auditResults.userId,
            audit_type: auditResults.auditType,
            findings: JSON.stringify(auditResults.findings),
            risk_score: auditResults.riskScore,
            recommendations: JSON.stringify(auditResults.recommendations),
            created_at: auditResults.timestamp
        });
    }

    async generateComplianceReport(startDate, endDate) {
        const auditResults = await SecurityAudit.findAll({
            where: {
                created_at: {
                    [Op.between]: [startDate, endDate]
                }
            },
            include: [{ model: User }]
        });

        const report = {
            reportId: require('uuid').v4(),
            period: { startDate, endDate },
            totalAudits: auditResults.length,
            averageRiskScore: 0,
            complianceStatus: 'COMPLIANT',
            findings: {
                critical: 0,
                high: 0,
                medium: 0,
                low: 0
            },
            recommendations: [],
            nonCompliantUsers: []
        };

        let totalRiskScore = 0;

        auditResults.forEach(audit => {
            totalRiskScore += audit.risk_score;
            
            const findings = JSON.parse(audit.findings);
            findings.forEach(finding => {
                report.findings[finding.severity.toLowerCase()]++;
            });

            if (audit.risk_score > 70) {
                report.nonCompliantUsers.push({
                    userId: audit.user_id,
                    username: audit.User.username,
                    riskScore: audit.risk_score
                });
            }
        });

        report.averageRiskScore = totalRiskScore / auditResults.length;
        
        if (report.findings.critical > 0 || report.averageRiskScore > 50) {
            report.complianceStatus = 'NON_COMPLIANT';
        } else if (report.findings.high > 5 || report.averageRiskScore > 30) {
            report.complianceStatus = 'NEEDS_ATTENTION';
        }

        return report;
    }
}

module.exports = SecurityAuditService;
```

### Authentication Configuration

```javascript
// config/authConfig.js
module.exports = {
    jwt: {
        secret: process.env.JWT_SECRET || 'your-super-secret-key',
        accessTokenExpiry: '15m',
        refreshTokenExpiry: '7d',
        algorithm: 'HS256'
    },
    
    session: {
        secret: process.env.SESSION_SECRET || 'session-secret-key',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true,
        sameSite: 'strict'
    },
    
    password: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
        maxAge: 90, // days
        preventReuse: 5 // number of previous passwords to check
    },
    
    mfa: {
        enabled: true,
        issuer: 'Inventory Management System',
        backupCodesCount: 8,
        windowSize: 1, // TOTP window size
        algorithm: 'sha1'
    },
    
    lockout: {
        maxAttempts: 5,
        lockoutDuration: 30 * 60 * 1000, // 30 minutes
        progressiveLockout: true
    },
    
    rateLimiting: {
        login: {
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 5 // 5 attempts per window
        },
        api: {
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 1000 // 1000 requests per window
        },
        passwordReset: {
            windowMs: 60 * 60 * 1000, // 1 hour
            max: 3 // 3 attempts per hour
        }
    },
    
    ipSecurity: {
        enabled: true,
        whitelist: process.env.IP_WHITELIST ? process.env.IP_WHITELIST.split(',') : [],
        blacklist: process.env.IP_BLACKLIST ? process.env.IP_BLACKLIST.split(',') : [],
        autoBlock: {
            enabled: true,
            threshold: 10, // failed attempts
            duration: 60 * 60 * 1000 // 1 hour block
        }
    },
    
    deviceTrust: {
        enabled: true,
        riskThreshold: 'MEDIUM',
        fingerprintFields: [
            'userAgent',
            'screenResolution',
            'timezone',
            'language',
            'plugins'
        ]
    },
    
    audit: {
        enabled: true,
        retentionDays: 365,
        logFailedAttempts: true,
        logSuccessfulLogins: true,
        logPermissionChanges: true,
        logDataAccess: true
    },
    
    permissions: {
        cacheExpiry: 300, // 5 minutes
        hierarchicalInheritance: true,
        dynamicPermissions: true,
        contextualRules: true
    },
    
    security: {
        requireMFAForAdmins: true,
        requireMFAForHighRisk: true,
        passwordComplexityCheck: true,
        sessionTimeout: 30 * 60 * 1000, // 30 minutes
        maxConcurrentSessions: 3,
        secureHeaders: true
    }
};
```

### Authentication Routes

```javascript
// routes/auth.js
const express = require('express');
const router = express.Router();
const AuthService = require('../services/AuthService');
const MFAService = require('../services/MFAService');
const authMiddleware = require('../middleware/authMiddleware');
const { body, validationResult } = require('express-validator');

const authService = new AuthService();
const mfaService = new MFAService();

// Apply rate limiting to auth routes
const authRateLimit = authMiddleware.createRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per window
    message: {
        success: false,
        message: 'Too many authentication attempts, please try again later'
    }
});

// Login Route
router.post('/login', 
    authRateLimit,
    [
        body('username').trim().notEmpty().withMessage('Username is required'),
        body('password').notEmpty().withMessage('Password is required'),
        body('deviceFingerprint').optional().isString()
    ],
    async (req, res) => {
        try {
            // Validate input
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { username, password, deviceFingerprint, rememberMe } = req.body;
            const ipAddress = authMiddleware.getClientIP(req);
            const userAgent = req.get('User-Agent');

            const loginResult = await authService.login({
                username,
                password,
                ipAddress,
                userAgent,
                deviceFingerprint,
                rememberMe: Boolean(rememberMe)
            });

            if (!loginResult.success) {
                return res.status(401).json(loginResult);
            }

            // Set secure cookies if remember me is enabled
            if (rememberMe && loginResult.refreshToken) {
                res.cookie('refreshToken', loginResult.refreshToken, {
                    httpOnly: true,
                    secure: process.env.NODE_ENV === 'production',
                    sameSite: 'strict',
                    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
                });
            }

            res.json(loginResult);
        } catch (error) {
            console.error('Login error:', error);
            res.status(500).json({
                success: false,
                message: 'Login failed'
            });
        }
    }
);

// MFA Verification Route
router.post('/verify-mfa',
    authRateLimit,
    [
        body('token').isLength({ min: 6, max: 6 }).withMessage('Invalid MFA token'),
        body('tempToken').notEmpty().withMessage('Temporary token required')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { token, tempToken } = req.body;
            
            const verificationResult = await authService.verifyMFA(tempToken, token);
            
            if (!verificationResult.success) {
                return res.status(401).json(verificationResult);
            }

            res.json(verificationResult);
        } catch (error) {
            console.error('MFA verification error:', error);
            res.status(500).json({
                success: false,
                message: 'MFA verification failed'
            });
        }
    }
);

//