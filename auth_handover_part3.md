# Developer Handover Document: Authentication & Authorization Services (Part 3)

*Continuation from Part 2...*

```javascript
// tests/services/AuthService.test.js (continued)
            expect(updateSpy).toHaveBeenCalledWith(
                expect.objectContaining({
                    failed_login_attempts: 5,
                    account_locked_until: expect.any(Date)
                }),
                expect.any(Object)
            );
        });
    });
});
```

### Integration Tests

```javascript
// tests/integration/auth.test.js
const request = require('supertest');
const app = require('../../app');
const { sequelize } = require('../../models');

describe('Authentication Integration Tests', () => {
    beforeEach(async () => {
        await sequelize.sync({ force: true });
        
        // Create test user
        await User.create({
            email: '<EMAIL>',
            username: 'testuser',
            password_hash: await bcrypt.hash('password123', 12),
            first_name: 'Test',
            last_name: 'User',
            is_active: true
        });
    });

    afterEach(async () => {
        await sequelize.drop();
    });

    describe('POST /auth/login', () => {
        test('should login with valid credentials', async () => {
            const response = await request(app)
                .post('/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'password123'
                });

            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('tokens');
            expect(response.body.tokens).toHaveProperty('accessToken');
            expect(response.body.tokens).toHaveProperty('refreshToken');
        });

        test('should reject invalid credentials', async () => {
            const response = await request(app)
                .post('/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'wrongpassword'
                });

            expect(response.status).toBe(401);
            expect(response.body).toHaveProperty('error');
        });

        test('should require MFA when enabled', async () => {
            // Enable MFA for test user
            await User.update(
                { mfa_enabled: true, mfa_secret: 'test-secret' },
                { where: { email: '<EMAIL>' } }
            );

            const response = await request(app)
                .post('/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'password123'
                });

            expect(response.status).toBe(200);
            expect(response.body.requiresMFA).toBe(true);
            expect(response.body).toHaveProperty('tempToken');
        });
    });

    describe('POST /auth/refresh', () => {
        test('should refresh valid token', async () => {
            // First login to get refresh token
            const loginResponse = await request(app)
                .post('/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'password123'
                });

            const { refreshToken } = loginResponse.body.tokens;

            const response = await request(app)
                .post('/auth/refresh')
                .send({ refreshToken });

            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('accessToken');
            expect(response.body).toHaveProperty('refreshToken');
        });

        test('should reject invalid refresh token', async () => {
            const response = await request(app)
                .post('/auth/refresh')
                .send({ refreshToken: 'invalid-token' });

            expect(response.status).toBe(401);
        });
    });
});
```

### Authorization Tests

```javascript
// tests/integration/authorization.test.js
describe('Authorization Integration Tests', () => {
    let adminUser, regularUser, managerUser;
    let adminToken, regularToken, managerToken;

    beforeEach(async () => {
        await sequelize.sync({ force: true });

        // Create roles
        const adminRole = await UserRole.create({
            role_name: 'admin',
            display_name: 'Administrator',
            description: 'Full system access'
        });

        const managerRole = await UserRole.create({
            role_name: 'manager',
            display_name: 'Manager',
            description: 'Department management access'
        });

        const userRole = await UserRole.create({
            role_name: 'user',
            display_name: 'User',
            description: 'Basic user access'
        });

        // Create permissions
        const userReadPermission = await RolePermission.create({
            role_id: adminRole.role_id,
            resource: 'users',
            action: 'read',
            granted_by: 1
        });

        // Create test users
        adminUser = await User.create({
            email: '<EMAIL>',
            username: 'admin',
            password_hash: await bcrypt.hash('password123', 12),
            first_name: 'Admin',
            last_name: 'User',
            is_active: true
        });

        regularUser = await User.create({
            email: '<EMAIL>',
            username: 'user',
            password_hash: await bcrypt.hash('password123', 12),
            first_name: 'Regular',
            last_name: 'User',
            is_active: true
        });

        // Assign roles
        await UserRoleAssignment.create({
            user_id: adminUser.user_id,
            role_id: adminRole.role_id,
            assigned_by: 1
        });

        await UserRoleAssignment.create({
            user_id: regularUser.user_id,
            role_id: userRole.role_id,
            assigned_by: 1
        });

        // Get tokens
        const adminLogin = await request(app)
            .post('/auth/login')
            .send({ email: '<EMAIL>', password: 'password123' });
        adminToken = adminLogin.body.tokens.accessToken;

        const userLogin = await request(app)
            .post('/auth/login')
            .send({ email: '<EMAIL>', password: 'password123' });
        regularToken = userLogin.body.tokens.accessToken;
    });

    describe('User Management Endpoints', () => {
        test('admin should access user list', async () => {
            const response = await request(app)
                .get('/users')
                .set('Authorization', `Bearer ${adminToken}`);

            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('users');
        });

        test('regular user should not access user list', async () => {
            const response = await request(app)
                .get('/users')
                .set('Authorization', `Bearer ${regularToken}`);

            expect(response.status).toBe(403);
        });

        test('user should access own profile', async () => {
            const response = await request(app)
                .get(`/users/${regularUser.user_id}`)
                .set('Authorization', `Bearer ${regularToken}`);

            expect(response.status).toBe(200);
            expect(response.body.user.user_id).toBe(regularUser.user_id);
        });

        test('user should not access other profiles', async () => {
            const response = await request(app)
                .get(`/users/${adminUser.user_id}`)
                .set('Authorization', `Bearer ${regularToken}`);

            expect(response.status).toBe(403);
        });
    });
});
```

## Performance Optimization

### Caching Strategy

```javascript
// services/CacheService.js
const Redis = require('ioredis');

class CacheService {
    constructor() {
        this.redis = new Redis(process.env.REDIS_URL);
        this.defaultTTL = 3600; // 1 hour
    }

    async cacheUserPermissions(userId, permissions) {
        const key = `user_permissions:${userId}`;
        await this.redis.setex(key, this.defaultTTL, JSON.stringify(permissions));
    }

    async getUserPermissions(userId) {
        const key = `user_permissions:${userId}`;
        const cached = await this.redis.get(key);
        return cached ? JSON.parse(cached) : null;
    }

    async cacheUserRoles(userId, roles) {
        const key = `user_roles:${userId}`;
        await this.redis.setex(key, this.defaultTTL, JSON.stringify(roles));
    }

    async getUserRoles(userId) {
        const key = `user_roles:${userId}`;
        const cached = await this.redis.get(key);
        return cached ? JSON.parse(cached) : null;
    }

    async invalidateUserCache(userId) {
        const keys = [
            `user_permissions:${userId}`,
            `user_roles:${userId}`,
            `user_restrictions:${userId}`
        ];
        
        await Promise.all(keys.map(key => this.redis.del(key)));
    }

    async cacheTokenBlacklist(jti, expiry) {
        const key = `blacklist:${jti}`;
        const ttl = Math.max(0, Math.floor((expiry - Date.now()) / 1000));
        await this.redis.setex(key, ttl, '1');
    }

    async isTokenBlacklisted(jti) {
        const key = `blacklist:${jti}`;
        const result = await this.redis.get(key);
        return result === '1';
    }

    async cacheFailedAttempts(identifier, attempts, lockoutTime = null) {
        const key = `failed_attempts:${identifier}`;
        if (lockoutTime) {
            const ttl = Math.floor((lockoutTime - Date.now()) / 1000);
            await this.redis.setex(key, ttl, attempts.toString());
        } else {
            await this.redis.setex(key, 900, attempts.toString()); // 15 minutes
        }
    }

    async getFailedAttempts(identifier) {
        const key = `failed_attempts:${identifier}`;
        const cached = await this.redis.get(key);
        return cached ? parseInt(cached) : 0;
    }

    async clearFailedAttempts(identifier) {
        const key = `failed_attempts:${identifier}`;
        await this.redis.del(key);
    }
}

module.exports = CacheService;
```

### Database Query Optimization

```javascript
// services/OptimizedAuthService.js
class OptimizedAuthService extends AuthService {
    constructor() {
        super();
        this.cache = new CacheService();
    }

    async getUserWithRolesAndPermissions(userId) {
        // Try cache first
        const cachedPermissions = await this.cache.getUserPermissions(userId);
        const cachedRoles = await this.cache.getUserRoles(userId);

        if (cachedPermissions && cachedRoles) {
            return {
                permissions: cachedPermissions,
                roles: cachedRoles
            };
        }

        // Single optimized query with joins
        const user = await User.findByPk(userId, {
            include: [{
                model: UserRoleAssignment,
                include: [{
                    model: UserRole,
                    include: [{
                        model: RolePermission,
                        where: { is_active: true }
                    }]
                }],
                where: { is_active: true }
            }],
            where: { is_active: true }
        });

        if (!user) return null;

        // Process permissions and roles
        const permissions = new Set();
        const roles = [];

        user.UserRoleAssignments.forEach(assignment => {
            const role = assignment.UserRole;
            roles.push({
                role_id: role.role_id,
                role_name: role.role_name,
                display_name: role.display_name
            });

            role.RolePermissions.forEach(permission => {
                permissions.add(`${permission.resource}:${permission.action}`);
            });
        });

        const permissionsArray = Array.from(permissions);

        // Cache the results
        await this.cache.cacheUserPermissions(userId, permissionsArray);
        await this.cache.cacheUserRoles(userId, roles);

        return {
            permissions: permissionsArray,
            roles: roles
        };
    }

    async hasPermissionOptimized(userId, resource, action) {
        const userAuth = await this.getUserWithRolesAndPermissions(userId);
        if (!userAuth) return false;

        const requiredPermission = `${resource}:${action}`;
        return userAuth.permissions.includes(requiredPermission);
    }

    async hasAnyPermissionOptimized(userId, permissions) {
        const userAuth = await this.getUserWithRolesAndPermissions(userId);
        if (!userAuth) return false;

        return permissions.some(permission => {
            const [resource, action] = permission.split(':');
            const requiredPermission = `${resource}:${action}`;
            return userAuth.permissions.includes(requiredPermission);
        });
    }
}
```

## Security Monitoring

### Audit Logging Service

```javascript
// services/AuditService.js
class AuditService {
    async logAuthEvent(userId, event, details = {}) {
        try {
            await AuditLog.create({
                user_id: userId,
                event_type: 'AUTH',
                event_action: event,
                resource_type: 'USER',
                resource_id: userId,
                details: {
                    ...details,
                    timestamp: new Date().toISOString(),
                    user_agent: details.userAgent,
                    ip_address: details.ipAddress
                },
                ip_address: details.ipAddress,
                user_agent: details.userAgent,
                created_at: new Date()
            });
        } catch (error) {
            console.error('Audit logging failed:', error);
        }
    }

    async logPermissionEvent(userId, event, resource, resourceId = null, details = {}) {
        try {
            await AuditLog.create({
                user_id: userId,
                event_type: 'PERMISSION',
                event_action: event,
                resource_type: resource.toUpperCase(),
                resource_id: resourceId,
                details: {
                    ...details,
                    timestamp: new Date().toISOString()
                },
                ip_address: details.ipAddress,
                user_agent: details.userAgent,
                created_at: new Date()
            });
        } catch (error) {
            console.error('Audit logging failed:', error);
        }
    }

    async getAuditLogs(filters = {}) {
        const {
            userId,
            eventType,
            eventAction,
            resourceType,
            startDate,
            endDate,
            page = 1,
            limit = 50
        } = filters;

        const offset = (page - 1) * limit;
        let whereClause = {};

        if (userId) whereClause.user_id = userId;
        if (eventType) whereClause.event_type = eventType;
        if (eventAction) whereClause.event_action = eventAction;
        if (resourceType) whereClause.resource_type = resourceType;

        if (startDate || endDate) {
            whereClause.created_at = {};
            if (startDate) whereClause.created_at[Op.gte] = new Date(startDate);
            if (endDate) whereClause.created_at[Op.lte] = new Date(endDate);
        }

        const logs = await AuditLog.findAndCountAll({
            where: whereClause,
            include: [{
                model: User,
                attributes: ['username', 'email', 'first_name', 'last_name']
            }],
            limit: parseInt(limit),
            offset: parseInt(offset),
            order: [['created_at', 'DESC']]
        });

        return {
            logs: logs.rows,
            pagination: {
                current_page: parseInt(page),
                total_pages: Math.ceil(logs.count / limit),
                total_logs: logs.count,
                per_page: parseInt(limit)
            }
        };
    }

    async getSecuritySummary(timeRange = '24h') {
        const timeRanges = {
            '1h': 1,
            '24h': 24,
            '7d': 24 * 7,
            '30d': 24 * 30
        };

        const hours = timeRanges[timeRange] || 24;
        const startDate = new Date(Date.now() - (hours * 60 * 60 * 1000));

        const summary = await AuditLog.findAll({
            attributes: [
                'event_action',
                [sequelize.fn('COUNT', sequelize.col('audit_id')), 'count']
            ],
            where: {
                event_type: 'AUTH',
                created_at: { [Op.gte]: startDate }
            },
            group: ['event_action'],
            raw: true
        });

        const failedLogins = await AuditLog.count({
            where: {
                event_action: 'LOGIN_FAILED',
                created_at: { [Op.gte]: startDate }
            }
        });

        const suspiciousActivity = await AuditLog.count({
            where: {
                event_action: { [Op.in]: ['ACCOUNT_LOCKED', 'MULTIPLE_FAILED_ATTEMPTS'] },
                created_at: { [Op.gte]: startDate }
            }
        });

        return {
            timeRange,
            summary,
            failedLogins,
            suspiciousActivity,
            generatedAt: new Date().toISOString()
        };
    }
}
```

### Security Monitoring Middleware

```javascript
// middleware/securityMonitoring.js
const AuditService = require('../services/AuditService');

const securityMonitoring = (req, res, next) => {
    const auditService = new AuditService();
    
    // Monitor failed authentication attempts
    const originalSend = res.send;
    res.send = function(data) {
        if (req.route && req.route.path.includes('/auth/')) {
            const statusCode = res.statusCode;
            const userId = req.user?.userId || null;
            
            if (statusCode === 401 && req.route.path.includes('/login')) {
                auditService.logAuthEvent(userId, 'LOGIN_FAILED', {
                    email: req.body?.email,
                    ipAddress: req.ip,
                    userAgent: req.get('User-Agent'),
                    endpoint: req.originalUrl
                });
            } else if (statusCode === 200 && req.route.path.includes('/login')) {
                auditService.logAuthEvent(userId, 'LOGIN_SUCCESS', {
                    email: req.body?.email,
                    ipAddress: req.ip,
                    userAgent: req.get('User-Agent')
                });
            }

            if (statusCode === 403) {
                auditService.logPermissionEvent(userId, 'ACCESS_DENIED', 'ENDPOINT', null, {
                    endpoint: req.originalUrl,
                    method: req.method,
                    ipAddress: req.ip,
                    userAgent: req.get('User-Agent')
                });
            }
        }

        originalSend.call(this, data);
    };

    next();
};

module.exports = securityMonitoring;
```

## Deployment Configuration

### Docker Configuration

```dockerfile
# Dockerfile.auth-service
FROM node:18-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy application code
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S authuser -u 1001

# Set ownership
RUN chown -R authuser:nodejs /app
USER authuser

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

CMD ["node", "server.js"]
```

### Docker Compose for Development

```yaml
# docker-compose.yml
version: '3.8'

services:
  auth-service:
    build:
      context: .
      dockerfile: Dockerfile.auth-service
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/inventory_dev
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-development-secret
    depends_on:
      - postgres
      - redis
    volumes:
      - .:/app
      - /app/node_modules

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=inventory_dev
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### Production Deployment Script

```bash
#!/bin/bash
# deploy-auth.sh

set -e

echo "Starting authentication service deployment..."

# Build and push Docker image
docker build -t inventory-auth:latest -f Dockerfile.auth-service .
docker tag inventory-auth:latest your-registry/inventory-auth:latest
docker push your-registry/inventory-auth:latest

# Deploy with zero downtime
kubectl set image deployment/auth-service auth-service=your-registry/inventory-auth:latest
kubectl rollout status deployment/auth-service

# Verify deployment
kubectl get pods -l app=auth-service
kubectl logs -l app=auth-service --tail=50

echo "Authentication service deployed successfully!"
```

### Kubernetes Configuration

```yaml
# k8s/auth-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  labels:
    app: auth-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
      - name: auth-service
        image: your-registry/inventory-auth:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: auth-secrets
              key: database-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: auth-secrets
              key: jwt-secret
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: auth-secrets
              key: redis-url
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 250m
            memory: 256Mi

---
apiVersion: v1
kind: Service
metadata:
  name: auth-service
spec:
  selector:
    app: auth-service
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP
```

## Maintenance Tasks

### Database Migration Script

```javascript
// scripts/migrate-auth-schema.js
const { sequelize } = require('../models');

async function migrateAuthSchema() {
    try {
        console.log('Starting authentication schema migration...');

        // Create tables if they don't exist
        await sequelize.sync({ alter: true });

        // Add indexes for performance
        await sequelize.query(`
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email 
            ON users(email);
        `);

        await sequelize.query(`
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_username 
            ON users(username);
        `);

        await sequelize.query(`
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_user_created 
            ON audit_logs(user_id, created_at DESC);
        `);

        await sequelize.query(`
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_user_expires 
            ON user_sessions(user_id, expires_at);
        `);

        console.log('Authentication schema migration completed successfully!');

    } catch (error) {
        console.error('Migration failed:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    migrateAuthSchema();
}

module.exports = migrateAuthSchema;
```

### Cleanup Script

```javascript
// scripts/cleanup-auth-data.js
const { Op } = require('sequelize');

async function cleanupAuthData() {
    try {
        console.log('Starting authentication data cleanup...');

        // Clean expired sessions
        const expiredSessions = await UserSession.destroy({
            where: {
                expires_at: { [Op.lt]: new Date() }
            }
        });
        console.log(`Cleaned ${expiredSessions} expired sessions`);

        // Clean old audit logs (older than 90 days)
        const oldAuditLogs = await AuditLog.destroy({
            where: {
                created_at: { 
                    [Op.lt]: new Date(Date.now() - (90 * 24 * 60 * 60 * 1000))
                }
            }
        });
        console.log(`Cleaned ${oldAuditLogs} old audit logs`);

        // Clean expired password reset tokens
        const expiredTokens = await PasswordResetToken.destroy({
            where: {
                expires_at: { [Op.lt]: new Date() }
            }
        });
        console.log(`Cleaned ${expiredTokens} expired password reset tokens`);

        // Unlock accounts that should no longer be locked
        const unlockedAccounts = await User.update(
            { 
                account_locked_until: null,
                failed_login_attempts: 0
            },
            {
                where: {
                    account_locked_until: { [Op.lt]: new Date() }
                }
            }
        );
        console.log(`Unlocked ${unlockedAccounts[0]} accounts`);

        console.log('Authentication data cleanup completed successfully!');

    } catch (error) {
        console.error('Cleanup failed:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    cleanupAuthData();
}

module.exports = cleanupAuthData;
```

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. JWT Token Issues
```javascript
// utils/tokenDebug.js
const jwt = require('jsonwebtoken');

function debugToken(token) {
    try {
        // Decode without verification to see payload
        const decoded = jwt.decode(token, { complete: true });
        console.log('Token Header:', decoded.header);
        console.log('Token Payload:', decoded.payload);
        
        // Check expiration
        const now = Math.floor(Date.now() / 1000);
        const exp = decoded.payload.exp;
        
        if (exp < now) {
            console.log('Token is expired');
            console.log(`Expired at: ${new Date(exp * 1000)}`);
            console.log(`Current time: ${new Date()}`);
        } else {
            console.log(`Token expires at: ${new Date(exp * 1000)}`);
        }
        
        return decoded;
    } catch (error) {
        console.error('Token decode error:', error.message);
        return null;
    }
}

module.exports = { debugToken };
```

#### 2. Permission Debugging
```javascript
// utils/permissionDebug.js
async function debugUserPermissions(userId) {
    const authService = new AuthorizationService();
    
    console.log(`Debugging permissions for user ${userId}:`);
    
    try {
        // Get user's roles
        const roles = await authService.getUserRoles(userId);
        console.log('User Roles:', roles);
        
        // Get user's permissions
        const permissions = await authService.getUserPermissions(userId);
        console.log('User Permissions:', permissions);
        
        // Check specific permission
        const hasInventoryRead = await authService.hasPermission(userId, 'inventory', 'read');
        console.log('Has inventory:read permission:', hasInventoryRead);
        
    } catch (error) {
        console.error('Debug error:', error);
    }
}

module.exports = { debugUserPermissions };
```

### Health Check Endpoints

```javascript
// routes/health.js
const express = require('express');
const router = express.Router();

// Basic health check
router.get('/health', async (req, res) => {
    try {
        const health = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            service: 'auth-service',
            version: process.env.npm_package_version || '1.0.0'
        };

        // Check database connection
        try {
            await sequelize.authenticate();
            health.database = 'connected';
        } catch (error) {
            health.database = 'disconnected';
            health.status = 'unhealthy';
        }

        // Check Redis connection
        try {
            const cache = new CacheService();
            await cache.redis.ping();
            health.cache = 'connected';
        } catch (error) {
            health.cache = 'disconnected';
            health.status = 'degraded';
        }

        const statusCode = health.status === 'healthy' ? 200 : 503;
        res.status(statusCode).json(health);

    } catch (error) {
        res.status(503).json({
            status: 'unhealthy',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Readiness check
router.get('/ready', async (req, res) => {
    try {
        // Check if service is ready to accept requests
        await sequelize.authenticate();
        
        res.status(200).json({
            status: 'ready',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(503).json({
            status: 'not ready',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

module.exports = router;
```

## API Documentation

### Authentication Endpoints Summary

| Endpoint | Method | Auth Required | Description |
|----------|--------|---------------|-------------|
| `/auth/login` | POST | No | User login with credentials |
| `/auth/refresh` | POST | No | Refresh access token |
| `/auth/logout` | POST | Yes | Logout and invalidate tokens |