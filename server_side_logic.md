# Developer Handover Document: Server-Side Logic and Processes for Inventory Management System

## Table of Contents
1. [Authentication & Authorization Services](#1-authentication--authorization-services)
2. [Item Master Management Logic](#2-item-master-management-logic)
3. [Inventory Tracking & Valuation Logic](#3-inventory-tracking--valuation-logic)
4. [Purchase Order Management Logic](#4-purchase-order-management-logic)
5. [Sales Order Management Logic](#5-sales-order-management-logic)
6. [Replenishment & Planning Logic](#6-replenishment--planning-logic)
7. [Warehouse Management Logic](#7-warehouse-management-logic)
8. [Bill of Materials (BOM) Logic](#8-bill-of-materials-bom-logic)
9. [Financial Integration Logic](#9-financial-integration-logic)
10. [Reporting & Analytics Logic](#10-reporting--analytics-logic)
11. [Integration & API Logic](#11-integration--api-logic)
12. [Background Job Processing](#12-background-job-processing)
13. [Data Validation & Business Rules](#13-data-validation--business-rules)
14. [Audit & Compliance Logic](#14-audit--compliance-logic)
15. [Performance Optimization Logic](#15-performance-optimization-logic)
16. [Error Handling & Recovery](#16-error-handling--recovery)
17. [Security & Data Protection](#17-security--data-protection)
18. [Implementation Guidelines](#18-implementation-guidelines)
19. [Testing Strategy](#19-testing-strategy)
20. [Deployment & Monitoring](#20-deployment--monitoring)

## Overview

This document provides comprehensive technical specifications for implementing the complete server-side logic and processes for the Inventory Management System. It serves as a detailed guide for developers taking over or maintaining this system, building upon the authentication and authorization services detailed in Parts 1-7.

### Technology Stack
- **Backend**: Node.js with Express.js framework
- **Database**: PostgreSQL with Prisma ORM
- **Cache**: Redis for session management and caching
- **Message Queue**: Bull Queue with Redis
- **File Storage**: Hostinger or local file system
- **Email Service**: Nodemailer with SMTP
- **PDF Generation**: Puppeteer
- **Excel Processing**: ExcelJS
- **Logging**: Winston with structured logging
- **Monitoring**: Prometheus metrics with Grafana

## 1. Authentication & Authorization Services

*Detailed implementation covered in auth_handover_part1.md through auth_handover_part7.md*

### Key Components Implemented
- **User Login Validation**: Verify credentials against hashed passwords
- **JWT Token Generation**: Create and manage access/refresh tokens
- **Multi-Factor Authentication**: SMS/Email OTP verification
- **Password Reset Flow**: Secure password recovery process
- **Session Management**: Track active user sessions and timeouts
- **Account Lockout**: Security measures for failed login attempts
- **Role-Based Access Control (RBAC)**: Check user permissions for resources
- **Permission Validation**: Verify user access to specific operations
- **Resource-Level Security**: Item/location-specific access controls
- **API Route Protection**: Middleware for endpoint security
- **Data Filtering**: Return only authorized data based on user roles

### Integration Points
All subsequent services integrate with the authentication system through:
- JWT token validation middleware
- Permission checking middleware
- Audit logging for all operations
- User context propagation

## 2. Item Master Management Logic

### Database Schema Requirements

#### ITEMS Table
```sql
CREATE TABLE items (
    item_id BIGSERIAL PRIMARY KEY,
    sku VARCHAR(50) UNIQUE NOT NULL,
    barcode VARCHAR(100) UNIQUE,
    item_name VARCHAR(200) NOT NULL,
    description TEXT,
    category_id BIGINT REFERENCES categories(category_id),
    brand_id BIGINT REFERENCES brands(brand_id),
    supplier_id BIGINT REFERENCES suppliers(supplier_id),
    base_uom_id BIGINT REFERENCES uoms(uom_id),
    item_type VARCHAR(20) DEFAULT 'STOCK', -- STOCK, NON_STOCK, SERVICE
    status VARCHAR(20) DEFAULT 'ACTIVE', -- ACTIVE, INACTIVE, DISCONTINUED
    abc_classification VARCHAR(1), -- A, B, C
    movement_type VARCHAR(20), -- FAST, MEDIUM, SLOW
    is_serialized BOOLEAN DEFAULT false,
    is_lot_tracked BOOLEAN DEFAULT false,
    has_expiry BOOLEAN DEFAULT false,
    shelf_life_days INTEGER,
    reorder_point DECIMAL(15,4),
    reorder_quantity DECIMAL(15,4),
    safety_stock DECIMAL(15,4),
    standard_cost DECIMAL(15,4),
    average_cost DECIMAL(15,4),
    last_cost DECIMAL(15,4),
    selling_price DECIMAL(15,4),
    minimum_price DECIMAL(15,4),
    weight DECIMAL(10,4),
    volume DECIMAL(10,4),
    dimensions JSONB, -- {length, width, height}
    storage_requirements JSONB, -- {temperature, humidity, special_handling}
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT REFERENCES users(user_id),
    updated_by BIGINT REFERENCES users(user_id)
);
```

#### ITEM_VARIANTS Table
```sql
CREATE TABLE item_variants (
    variant_id BIGSERIAL PRIMARY KEY,
    parent_item_id BIGINT NOT NULL REFERENCES items(item_id),
    variant_sku VARCHAR(50) UNIQUE NOT NULL,
    variant_name VARCHAR(200) NOT NULL,
    variant_attributes JSONB, -- {color: 'red', size: 'large'}
    price_difference DECIMAL(15,4) DEFAULT 0,
    cost_difference DECIMAL(15,4) DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### ITEM_UOMS Table
```sql
CREATE TABLE item_uoms (
    item_uom_id BIGSERIAL PRIMARY KEY,
    item_id BIGINT NOT NULL REFERENCES items(item_id),
    uom_id BIGINT NOT NULL REFERENCES uoms(uom_id),
    conversion_factor DECIMAL(15,6) NOT NULL,
    is_base_uom BOOLEAN DEFAULT false,
    is_purchase_uom BOOLEAN DEFAULT false,
    is_sales_uom BOOLEAN DEFAULT false,
    barcode VARCHAR(100),
    UNIQUE(item_id, uom_id)
);
```

### Item Service Implementation

#### Core Item Service
```javascript
// services/ItemService.js
const { Item, Category, Brand, Supplier, UOM, ItemUOM, ItemVariant } = require('../models');
const { ValidationError, BusinessLogicError } = require('../utils/errors');
const AuditService = require('./AuditService');
const InventoryService = require('./InventoryService');

class ItemService {
    constructor() {
        this.auditService = new AuditService();
        this.inventoryService = new InventoryService();
    }

    async createItem(itemData, userId) {
        const transaction = await sequelize.transaction();

        try {
            // Validate required fields
            await this.validateItemData(itemData);

            // Check SKU uniqueness
            await this.validateUniqueSKU(itemData.sku);

            // Validate barcode if provided
            if (itemData.barcode) {
                await this.validateUniqueBarcode(itemData.barcode);
            }

            // Validate category exists
            await this.validateCategoryExists(itemData.category_id);

            // Create item
            const item = await Item.create({
                ...itemData,
                created_by: userId,
                updated_by: userId
            }, { transaction });

            // Create base UOM relationship
            if (itemData.base_uom_id) {
                await ItemUOM.create({
                    item_id: item.item_id,
                    uom_id: itemData.base_uom_id,
                    conversion_factor: 1.0,
                    is_base_uom: true,
                    is_purchase_uom: true,
                    is_sales_uom: true
                }, { transaction });
            }

            // Create additional UOMs if provided
            if (itemData.additional_uoms) {
                await this.createItemUOMs(item.item_id, itemData.additional_uoms, transaction);
            }

            // Create variants if provided
            if (itemData.variants) {
                await this.createItemVariants(item.item_id, itemData.variants, transaction);
            }

            await transaction.commit();

            // Log audit event
            await this.auditService.logEvent(userId, 'ITEM_CREATED', 'ITEM', item.item_id, {
                sku: item.sku,
                name: item.item_name
            });

            return await this.getItemById(item.item_id);

        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    async updateItem(itemId, updateData, userId) {
        const transaction = await sequelize.transaction();

        try {
            const existingItem = await Item.findByPk(itemId, { transaction });
            if (!existingItem) {
                throw new ValidationError('Item not found');
            }

            // Validate update data
            await this.validateItemUpdateData(itemId, updateData);

            // Check for critical changes that require validation
            await this.validateCriticalChanges(existingItem, updateData);

            // Update item
            await Item.update({
                ...updateData,
                updated_by: userId,
                updated_at: new Date()
            }, {
                where: { item_id: itemId },
                transaction
            });

            // Handle UOM updates
            if (updateData.uoms) {
                await this.updateItemUOMs(itemId, updateData.uoms, transaction);
            }

            // Handle variant updates
            if (updateData.variants) {
                await this.updateItemVariants(itemId, updateData.variants, transaction);
            }

            await transaction.commit();

            // Log audit event
            await this.auditService.logEvent(userId, 'ITEM_UPDATED', 'ITEM', itemId, {
                changes: updateData
            });

            return await this.getItemById(itemId);

        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    async deleteItem(itemId, userId) {
        const transaction = await sequelize.transaction();

        try {
            const item = await Item.findByPk(itemId, { transaction });
            if (!item) {
                throw new ValidationError('Item not found');
            }

            // Check if item can be deleted
            await this.validateItemDeletion(itemId);

            // Soft delete - update status instead of actual deletion
            await Item.update({
                status: 'DELETED',
                updated_by: userId,
                updated_at: new Date()
            }, {
                where: { item_id: itemId },
                transaction
            });

            // Deactivate variants
            await ItemVariant.update({
                is_active: false
            }, {
                where: { parent_item_id: itemId },
                transaction
            });

            await transaction.commit();

            // Log audit event
            await this.auditService.logEvent(userId, 'ITEM_DELETED', 'ITEM', itemId, {
                sku: item.sku,
                name: item.item_name
            });

            return { success: true, message: 'Item deleted successfully' };

        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }
}

    // Validation methods
    async validateItemData(itemData) {
        const errors = [];

        if (!itemData.sku || itemData.sku.trim().length === 0) {
            errors.push('SKU is required');
        }

        if (!itemData.item_name || itemData.item_name.trim().length === 0) {
            errors.push('Item name is required');
        }

        if (!itemData.category_id) {
            errors.push('Category is required');
        }

        if (!itemData.base_uom_id) {
            errors.push('Base UOM is required');
        }

        if (errors.length > 0) {
            throw new ValidationError(errors.join(', '));
        }
    }

    async validateUniqueSKU(sku, excludeItemId = null) {
        const whereClause = { sku };
        if (excludeItemId) {
            whereClause.item_id = { [Op.ne]: excludeItemId };
        }

        const existingItem = await Item.findOne({ where: whereClause });
        if (existingItem) {
            throw new ValidationError('SKU already exists');
        }
    }

    async validateUniqueBarcode(barcode, excludeItemId = null) {
        const whereClause = { barcode };
        if (excludeItemId) {
            whereClause.item_id = { [Op.ne]: excludeItemId };
        }

        const existingItem = await Item.findOne({ where: whereClause });
        if (existingItem) {
            throw new ValidationError('Barcode already exists');
        }
    }

    async validateCategoryExists(categoryId) {
        const category = await Category.findByPk(categoryId);
        if (!category) {
            throw new ValidationError('Category not found');
        }
    }

    async validateItemDeletion(itemId) {
        // Check if item has inventory
        const hasInventory = await this.inventoryService.hasInventory(itemId);
        if (hasInventory) {
            throw new BusinessLogicError('Cannot delete item with existing inventory');
        }

        // Check if item has active orders
        const hasActiveOrders = await this.hasActiveOrders(itemId);
        if (hasActiveOrders) {
            throw new BusinessLogicError('Cannot delete item with active orders');
        }
    }

    async validateCriticalChanges(existingItem, updateData) {
        // Validate UOM changes
        if (updateData.base_uom_id && updateData.base_uom_id !== existingItem.base_uom_id) {
            const hasTransactions = await this.hasTransactions(existingItem.item_id);
            if (hasTransactions) {
                throw new BusinessLogicError('Cannot change base UOM for item with existing transactions');
            }
        }

        // Validate serialization changes
        if (updateData.is_serialized !== undefined &&
            updateData.is_serialized !== existingItem.is_serialized) {
            const hasInventory = await this.inventoryService.hasInventory(existingItem.item_id);
            if (hasInventory) {
                throw new BusinessLogicError('Cannot change serialization for item with existing inventory');
            }
        }
    }

    // ABC Classification Logic
    async performABCClassification() {
        const items = await Item.findAll({
            where: { status: 'ACTIVE' },
            include: [{
                model: InventoryTransaction,
                where: {
                    transaction_date: {
                        [Op.gte]: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000) // Last 12 months
                    }
                },
                required: false
            }]
        });

        // Calculate annual consumption value for each item
        const itemValues = items.map(item => {
            const totalValue = item.InventoryTransactions
                .filter(t => t.transaction_type === 'ISSUE')
                .reduce((sum, t) => sum + (t.quantity * t.unit_cost), 0);

            return {
                item_id: item.item_id,
                annual_value: totalValue
            };
        });

        // Sort by value descending
        itemValues.sort((a, b) => b.annual_value - a.annual_value);

        const totalValue = itemValues.reduce((sum, item) => sum + item.annual_value, 0);
        let cumulativeValue = 0;

        // Classify items
        for (let i = 0; i < itemValues.length; i++) {
            cumulativeValue += itemValues[i].annual_value;
            const percentage = (cumulativeValue / totalValue) * 100;

            let classification;
            if (percentage <= 80) {
                classification = 'A';
            } else if (percentage <= 95) {
                classification = 'B';
            } else {
                classification = 'C';
            }

            await Item.update(
                { abc_classification: classification },
                { where: { item_id: itemValues[i].item_id } }
            );
        }
    }

    // Movement Analysis Logic
    async analyzeMovementPatterns() {
        const cutoffDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000); // Last 3 months

        const items = await Item.findAll({
            where: { status: 'ACTIVE' },
            include: [{
                model: InventoryTransaction,
                where: {
                    transaction_date: { [Op.gte]: cutoffDate },
                    transaction_type: 'ISSUE'
                },
                required: false
            }]
        });

        for (const item of items) {
            const totalIssued = item.InventoryTransactions
                .reduce((sum, t) => sum + t.quantity, 0);

            const averageMonthlyMovement = totalIssued / 3;

            let movementType;
            if (averageMonthlyMovement >= item.reorder_quantity * 2) {
                movementType = 'FAST';
            } else if (averageMonthlyMovement >= item.reorder_quantity * 0.5) {
                movementType = 'MEDIUM';
            } else {
                movementType = 'SLOW';
            }

            await Item.update(
                { movement_type: movementType },
                { where: { item_id: item.item_id } }
            );
        }
    }
}

module.exports = ItemService;
```

## 3. Inventory Tracking & Valuation Logic

### Database Schema Requirements

#### INVENTORY_BALANCES Table
```sql
CREATE TABLE inventory_balances (
    balance_id BIGSERIAL PRIMARY KEY,
    item_id BIGINT NOT NULL REFERENCES items(item_id),
    location_id BIGINT NOT NULL REFERENCES locations(location_id),
    lot_id BIGINT REFERENCES lots(lot_id),
    serial_number VARCHAR(100),
    quantity_on_hand DECIMAL(15,4) NOT NULL DEFAULT 0,
    quantity_reserved DECIMAL(15,4) NOT NULL DEFAULT 0,
    quantity_available DECIMAL(15,4) GENERATED ALWAYS AS (quantity_on_hand - quantity_reserved) STORED,
    unit_cost DECIMAL(15,4),
    total_value DECIMAL(15,4) GENERATED ALWAYS AS (quantity_on_hand * unit_cost) STORED,
    last_movement_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(item_id, location_id, lot_id, serial_number)
);
```

#### INVENTORY_TRANSACTIONS Table
```sql
CREATE TABLE inventory_transactions (
    transaction_id BIGSERIAL PRIMARY KEY,
    item_id BIGINT NOT NULL REFERENCES items(item_id),
    location_id BIGINT NOT NULL REFERENCES locations(location_id),
    lot_id BIGINT REFERENCES lots(lot_id),
    serial_number VARCHAR(100),
    transaction_type VARCHAR(20) NOT NULL, -- RECEIPT, ISSUE, TRANSFER, ADJUSTMENT
    transaction_subtype VARCHAR(30), -- PURCHASE, SALE, PRODUCTION, CYCLE_COUNT
    reference_type VARCHAR(20), -- PO, SO, TRANSFER, ADJUSTMENT
    reference_id BIGINT,
    quantity DECIMAL(15,4) NOT NULL,
    unit_cost DECIMAL(15,4),
    total_cost DECIMAL(15,4),
    transaction_date TIMESTAMP NOT NULL,
    posting_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    created_by BIGINT REFERENCES users(user_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### COST_LAYERS Table
```sql
CREATE TABLE cost_layers (
    layer_id BIGSERIAL PRIMARY KEY,
    item_id BIGINT NOT NULL REFERENCES items(item_id),
    location_id BIGINT NOT NULL REFERENCES locations(location_id),
    lot_id BIGINT REFERENCES lots(lot_id),
    receipt_date TIMESTAMP NOT NULL,
    unit_cost DECIMAL(15,4) NOT NULL,
    original_quantity DECIMAL(15,4) NOT NULL,
    remaining_quantity DECIMAL(15,4) NOT NULL,
    reference_type VARCHAR(20),
    reference_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Inventory Service Implementation

```javascript
// services/InventoryService.js
const { InventoryBalance, InventoryTransaction, CostLayer, Item, Location, Lot } = require('../models');
const { ValidationError, BusinessLogicError } = require('../utils/errors');

class InventoryService {
    constructor() {
        this.costingMethod = process.env.COSTING_METHOD || 'FIFO'; // FIFO, LIFO, AVERAGE
    }

    async processInventoryTransaction(transactionData, userId) {
        const transaction = await sequelize.transaction();

        try {
            // Validate transaction data
            await this.validateTransactionData(transactionData);

            // Create inventory transaction record
            const invTransaction = await InventoryTransaction.create({
                ...transactionData,
                posting_date: new Date(),
                created_by: userId
            }, { transaction });

            // Update inventory balance
            await this.updateInventoryBalance(transactionData, transaction);

            // Handle cost layers for receipts
            if (transactionData.transaction_type === 'RECEIPT') {
                await this.createCostLayer(transactionData, transaction);
            }

            // Handle cost consumption for issues
            if (transactionData.transaction_type === 'ISSUE') {
                await this.consumeCostLayers(transactionData, transaction);
            }

            await transaction.commit();

            return invTransaction;

        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    async updateInventoryBalance(transactionData, transaction) {
        const {
            item_id,
            location_id,
            lot_id,
            serial_number,
            quantity,
            unit_cost,
            transaction_type
        } = transactionData;

        // Find or create balance record
        let balance = await InventoryBalance.findOne({
            where: {
                item_id,
                location_id,
                lot_id: lot_id || null,
                serial_number: serial_number || null
            },
            transaction
        });

        if (!balance) {
            balance = await InventoryBalance.create({
                item_id,
                location_id,
                lot_id,
                serial_number,
                quantity_on_hand: 0,
                quantity_reserved: 0,
                unit_cost: unit_cost || 0
            }, { transaction });
        }

        // Calculate new quantities based on transaction type
        let newQuantityOnHand = balance.quantity_on_hand;
        let newQuantityReserved = balance.quantity_reserved;

        switch (transaction_type) {
            case 'RECEIPT':
                newQuantityOnHand += quantity;
                break;
            case 'ISSUE':
                newQuantityOnHand -= quantity;
                break;
            case 'TRANSFER':
                // Handle in separate transfer logic
                break;
            case 'ADJUSTMENT':
                newQuantityOnHand = quantity; // Absolute adjustment
                break;
            case 'RESERVATION':
                newQuantityReserved += quantity;
                break;
            case 'UNRESERVATION':
                newQuantityReserved -= quantity;
                break;
        }

        // Validate negative stock if not allowed
        if (newQuantityOnHand < 0 && !this.allowNegativeStock()) {
            throw new BusinessLogicError('Insufficient inventory');
        }

        // Update balance
        await balance.update({
            quantity_on_hand: newQuantityOnHand,
            quantity_reserved: Math.max(0, newQuantityReserved),
            unit_cost: unit_cost || balance.unit_cost,
            last_movement_date: new Date()
        }, { transaction });
    }

    async createCostLayer(transactionData, transaction) {
        const {
            item_id,
            location_id,
            lot_id,
            quantity,
            unit_cost,
            transaction_date,
            reference_type,
            reference_id
        } = transactionData;

        await CostLayer.create({
            item_id,
            location_id,
            lot_id,
            receipt_date: transaction_date,
            unit_cost,
            original_quantity: quantity,
            remaining_quantity: quantity,
            reference_type,
            reference_id
        }, { transaction });
    }

    async consumeCostLayers(transactionData, transaction) {
        const {
            item_id,
            location_id,
            lot_id,
            quantity
        } = transactionData;

        let remainingToConsume = quantity;

        // Get cost layers based on costing method
        const costLayers = await this.getCostLayersForConsumption(
            item_id,
            location_id,
            lot_id,
            transaction
        );

        for (const layer of costLayers) {
            if (remainingToConsume <= 0) break;

            const consumeFromLayer = Math.min(remainingToConsume, layer.remaining_quantity);

            await layer.update({
                remaining_quantity: layer.remaining_quantity - consumeFromLayer
            }, { transaction });

            remainingToConsume -= consumeFromLayer;
        }

        if (remainingToConsume > 0 && !this.allowNegativeStock()) {
            throw new BusinessLogicError('Insufficient cost layers for consumption');
        }
    }

    async getCostLayersForConsumption(itemId, locationId, lotId, transaction) {
        const whereClause = {
            item_id: itemId,
            location_id: locationId,
            remaining_quantity: { [Op.gt]: 0 }
        };

        if (lotId) {
            whereClause.lot_id = lotId;
        }

        let orderClause;
        switch (this.costingMethod) {
            case 'FIFO':
                orderClause = [['receipt_date', 'ASC']];
                break;
            case 'LIFO':
                orderClause = [['receipt_date', 'DESC']];
                break;
            case 'AVERAGE':
                // For average costing, we'll handle differently
                orderClause = [['receipt_date', 'ASC']];
                break;
        }

        return await CostLayer.findAll({
            where: whereClause,
            order: orderClause,
            transaction
        });
    }

    async calculateAverageCost(itemId, locationId, lotId = null) {
        const whereClause = {
            item_id: itemId,
            location_id: locationId,
            remaining_quantity: { [Op.gt]: 0 }
        };

        if (lotId) {
            whereClause.lot_id = lotId;
        }

        const costLayers = await CostLayer.findAll({
            where: whereClause
        });

        if (costLayers.length === 0) return 0;

        const totalValue = costLayers.reduce((sum, layer) =>
            sum + (layer.remaining_quantity * layer.unit_cost), 0);
        const totalQuantity = costLayers.reduce((sum, layer) =>
            sum + layer.remaining_quantity, 0);

        return totalQuantity > 0 ? totalValue / totalQuantity : 0;
    }

    allowNegativeStock() {
        return process.env.ALLOW_NEGATIVE_STOCK === 'true';
    }
}

module.exports = InventoryService;
```

## 4. Purchase Order Management Logic

### Database Schema Requirements

#### PURCHASE_ORDERS Table
```sql
CREATE TABLE purchase_orders (
    po_id BIGSERIAL PRIMARY KEY,
    po_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id BIGINT NOT NULL REFERENCES suppliers(supplier_id),
    po_date DATE NOT NULL,
    expected_delivery_date DATE,
    status VARCHAR(20) DEFAULT 'DRAFT', -- DRAFT, PENDING_APPROVAL, APPROVED, SENT, PARTIALLY_RECEIVED, RECEIVED, CANCELLED
    approval_status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED
    currency_code VARCHAR(3) DEFAULT 'PHP',
    exchange_rate DECIMAL(10,6) DEFAULT 1.0,
    subtotal DECIMAL(15,4) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(15,4) NOT NULL DEFAULT 0,
    total_amount DECIMAL(15,4) NOT NULL DEFAULT 0,
    payment_terms VARCHAR(50),
    delivery_terms VARCHAR(100),
    notes TEXT,
    created_by BIGINT REFERENCES users(user_id),
    approved_by BIGINT REFERENCES users(user_id),
    approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### PURCHASE_ORDER_LINES Table
```sql
CREATE TABLE purchase_order_lines (
    po_line_id BIGSERIAL PRIMARY KEY,
    po_id BIGINT NOT NULL REFERENCES purchase_orders(po_id),
    item_id BIGINT NOT NULL REFERENCES items(item_id),
    line_number INTEGER NOT NULL,
    quantity_ordered DECIMAL(15,4) NOT NULL,
    quantity_received DECIMAL(15,4) DEFAULT 0,
    quantity_remaining DECIMAL(15,4) GENERATED ALWAYS AS (quantity_ordered - quantity_received) STORED,
    unit_price DECIMAL(15,4) NOT NULL,
    line_total DECIMAL(15,4) GENERATED ALWAYS AS (quantity_ordered * unit_price) STORED,
    uom_id BIGINT REFERENCES uoms(uom_id),
    expected_delivery_date DATE,
    notes TEXT,
    UNIQUE(po_id, line_number)
);
```

### Purchase Order Service Implementation

```javascript
// services/PurchaseOrderService.js
const { PurchaseOrder, PurchaseOrderLine, Supplier, Item, User } = require('../models');
const { ValidationError, BusinessLogicError } = require('../utils/errors');
const ApprovalService = require('./ApprovalService');
const InventoryService = require('./InventoryService');
const NotificationService = require('./NotificationService');

class PurchaseOrderService {
    constructor() {
        this.approvalService = new ApprovalService();
        this.inventoryService = new InventoryService();
        this.notificationService = new NotificationService();
    }

    async createPurchaseOrder(poData, userId) {
        const transaction = await sequelize.transaction();

        try {
            // Validate PO data
            await this.validatePOData(poData);

            // Generate PO number
            const poNumber = await this.generatePONumber();

            // Create PO header
            const po = await PurchaseOrder.create({
                po_number: poNumber,
                supplier_id: poData.supplier_id,
                po_date: poData.po_date || new Date(),
                expected_delivery_date: poData.expected_delivery_date,
                currency_code: poData.currency_code || 'PHP',
                exchange_rate: poData.exchange_rate || 1.0,
                payment_terms: poData.payment_terms,
                delivery_terms: poData.delivery_terms,
                notes: poData.notes,
                created_by: userId
            }, { transaction });

            // Create PO lines
            let lineNumber = 1;
            let subtotal = 0;

            for (const lineData of poData.lines) {
                await this.validatePOLineData(lineData);

                const poLine = await PurchaseOrderLine.create({
                    po_id: po.po_id,
                    item_id: lineData.item_id,
                    line_number: lineNumber++,
                    quantity_ordered: lineData.quantity_ordered,
                    unit_price: lineData.unit_price,
                    uom_id: lineData.uom_id,
                    expected_delivery_date: lineData.expected_delivery_date,
                    notes: lineData.notes
                }, { transaction });

                subtotal += lineData.quantity_ordered * lineData.unit_price;
            }

            // Calculate totals
            const taxAmount = subtotal * (poData.tax_rate || 0.12); // 12% VAT default
            const totalAmount = subtotal + taxAmount;

            // Update PO totals
            await po.update({
                subtotal,
                tax_amount: taxAmount,
                total_amount: totalAmount
            }, { transaction });

            await transaction.commit();

            // Check if approval is required
            if (await this.requiresApproval(totalAmount, userId)) {
                await this.submitForApproval(po.po_id, userId);
            } else {
                await this.autoApprove(po.po_id, userId);
            }

            return await this.getPOById(po.po_id);

        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    async submitForApproval(poId, userId) {
        const po = await PurchaseOrder.findByPk(poId);
        if (!po) {
            throw new ValidationError('Purchase order not found');
        }

        // Update status
        await po.update({
            status: 'PENDING_APPROVAL',
            approval_status: 'PENDING'
        });

        // Submit to approval workflow
        await this.approvalService.submitForApproval({
            document_type: 'PURCHASE_ORDER',
            document_id: poId,
            amount: po.total_amount,
            submitted_by: userId
        });

        // Notify approvers
        const approvers = await this.approvalService.getApprovers('PURCHASE_ORDER', po.total_amount);
        for (const approver of approvers) {
            await this.notificationService.sendNotification({
                user_id: approver.user_id,
                type: 'APPROVAL_REQUEST',
                title: 'Purchase Order Approval Required',
                message: `PO ${po.po_number} requires your approval`,
                data: { po_id: poId }
            });
        }
    }

    async approvePO(poId, userId, comments = null) {
        const transaction = await sequelize.transaction();

        try {
            const po = await PurchaseOrder.findByPk(poId, { transaction });
            if (!po) {
                throw new ValidationError('Purchase order not found');
            }

            if (po.approval_status !== 'PENDING') {
                throw new BusinessLogicError('Purchase order is not pending approval');
            }

            // Process approval
            const approvalResult = await this.approvalService.processApproval({
                document_type: 'PURCHASE_ORDER',
                document_id: poId,
                approved_by: userId,
                comments
            });

            if (approvalResult.final_approval) {
                await po.update({
                    status: 'APPROVED',
                    approval_status: 'APPROVED',
                    approved_by: userId,
                    approved_at: new Date()
                }, { transaction });
            }

            await transaction.commit();

            // Notify requester
            await this.notificationService.sendNotification({
                user_id: po.created_by,
                type: 'APPROVAL_UPDATE',
                title: 'Purchase Order Approved',
                message: `PO ${po.po_number} has been approved`,
                data: { po_id: poId }
            });

            return approvalResult;

        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    async receiveGoods(receiptData, userId) {
        const transaction = await sequelize.transaction();

        try {
            const { po_id, receipts } = receiptData;

            const po = await PurchaseOrder.findByPk(po_id, {
                include: [PurchaseOrderLine],
                transaction
            });

            if (!po) {
                throw new ValidationError('Purchase order not found');
            }

            if (po.status !== 'APPROVED' && po.status !== 'PARTIALLY_RECEIVED') {
                throw new BusinessLogicError('Purchase order is not in receivable status');
            }

            for (const receipt of receipts) {
                await this.processLineReceipt(po, receipt, userId, transaction);
            }

            // Update PO status
            const allLinesReceived = await this.checkAllLinesReceived(po_id, transaction);
            const newStatus = allLinesReceived ? 'RECEIVED' : 'PARTIALLY_RECEIVED';

            await po.update({ status: newStatus }, { transaction });

            await transaction.commit();

            return { success: true, message: 'Goods received successfully' };

        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    async processLineReceipt(po, receipt, userId, transaction) {
        const { po_line_id, quantity_received, unit_cost, lot_number, expiry_date } = receipt;

        const poLine = await PurchaseOrderLine.findByPk(po_line_id, { transaction });
        if (!poLine) {
            throw new ValidationError('Purchase order line not found');
        }

        // Validate receipt quantity
        const remainingQuantity = poLine.quantity_ordered - poLine.quantity_received;
        if (quantity_received > remainingQuantity) {
            throw new BusinessLogicError('Receipt quantity exceeds remaining quantity');
        }

        // Update PO line
        await poLine.update({
            quantity_received: poLine.quantity_received + quantity_received
        }, { transaction });

        // Create inventory transaction
        await this.inventoryService.processInventoryTransaction({
            item_id: poLine.item_id,
            location_id: receipt.location_id,
            lot_id: receipt.lot_id,
            transaction_type: 'RECEIPT',
            transaction_subtype: 'PURCHASE',
            reference_type: 'PO',
            reference_id: po.po_id,
            quantity: quantity_received,
            unit_cost: unit_cost || poLine.unit_price,
            transaction_date: new Date()
        }, userId);
    }
}

module.exports = PurchaseOrderService;
```

## 5. Sales Order Management Logic

### Database Schema Requirements

#### SALES_ORDERS Table
```sql
CREATE TABLE sales_orders (
    so_id BIGSERIAL PRIMARY KEY,
    so_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id BIGINT NOT NULL REFERENCES customers(customer_id),
    so_date DATE NOT NULL,
    required_date DATE,
    promised_date DATE,
    status VARCHAR(20) DEFAULT 'DRAFT', -- DRAFT, CONFIRMED, RESERVED, PICKED, PACKED, SHIPPED, DELIVERED, CANCELLED
    priority VARCHAR(10) DEFAULT 'NORMAL', -- LOW, NORMAL, HIGH, URGENT
    currency_code VARCHAR(3) DEFAULT 'PHP',
    exchange_rate DECIMAL(10,6) DEFAULT 1.0,
    subtotal DECIMAL(15,4) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(15,4) DEFAULT 0,
    tax_amount DECIMAL(15,4) NOT NULL DEFAULT 0,
    shipping_amount DECIMAL(15,4) DEFAULT 0,
    total_amount DECIMAL(15,4) NOT NULL DEFAULT 0,
    payment_terms VARCHAR(50),
    shipping_method VARCHAR(50),
    shipping_address JSONB,
    billing_address JSONB,
    notes TEXT,
    created_by BIGINT REFERENCES users(user_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### SALES_ORDER_LINES Table
```sql
CREATE TABLE sales_order_lines (
    so_line_id BIGSERIAL PRIMARY KEY,
    so_id BIGINT NOT NULL REFERENCES sales_orders(so_id),
    item_id BIGINT NOT NULL REFERENCES items(item_id),
    line_number INTEGER NOT NULL,
    quantity_ordered DECIMAL(15,4) NOT NULL,
    quantity_reserved DECIMAL(15,4) DEFAULT 0,
    quantity_picked DECIMAL(15,4) DEFAULT 0,
    quantity_shipped DECIMAL(15,4) DEFAULT 0,
    unit_price DECIMAL(15,4) NOT NULL,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(15,4) DEFAULT 0,
    line_total DECIMAL(15,4) GENERATED ALWAYS AS ((quantity_ordered * unit_price) - discount_amount) STORED,
    uom_id BIGINT REFERENCES uoms(uom_id),
    required_date DATE,
    promised_date DATE,
    notes TEXT,
    UNIQUE(so_id, line_number)
);
```

### Sales Order Service Implementation

```javascript
// services/SalesOrderService.js
const { SalesOrder, SalesOrderLine, Customer, Item, InventoryBalance } = require('../models');
const { ValidationError, BusinessLogicError } = require('../utils/errors');
const InventoryService = require('./InventoryService');
const PricingService = require('./PricingService');
const TaxService = require('./TaxService');

class SalesOrderService {
    constructor() {
        this.inventoryService = new InventoryService();
        this.pricingService = new PricingService();
        this.taxService = new TaxService();
    }

    async createSalesOrder(soData, userId) {
        const transaction = await sequelize.transaction();

        try {
            // Validate customer credit limit
            await this.validateCustomerCredit(soData.customer_id, soData.total_amount);

            // Generate SO number
            const soNumber = await this.generateSONumber();

            // Create SO header
            const so = await SalesOrder.create({
                so_number: soNumber,
                customer_id: soData.customer_id,
                so_date: soData.so_date || new Date(),
                required_date: soData.required_date,
                currency_code: soData.currency_code || 'PHP',
                exchange_rate: soData.exchange_rate || 1.0,
                payment_terms: soData.payment_terms,
                shipping_method: soData.shipping_method,
                shipping_address: soData.shipping_address,
                billing_address: soData.billing_address,
                notes: soData.notes,
                created_by: userId
            }, { transaction });

            // Create SO lines with pricing and availability checks
            let lineNumber = 1;
            let subtotal = 0;

            for (const lineData of soData.lines) {
                // Check stock availability
                const availability = await this.checkStockAvailability(
                    lineData.item_id,
                    lineData.quantity_ordered
                );

                if (!availability.available && !this.allowBackorders()) {
                    throw new BusinessLogicError(`Insufficient stock for item ${lineData.item_id}`);
                }

                // Get pricing
                const unitPrice = await this.pricingService.getPrice(
                    lineData.item_id,
                    soData.customer_id,
                    lineData.quantity_ordered
                );

                const soLine = await SalesOrderLine.create({
                    so_id: so.so_id,
                    item_id: lineData.item_id,
                    line_number: lineNumber++,
                    quantity_ordered: lineData.quantity_ordered,
                    unit_price: unitPrice,
                    discount_percent: lineData.discount_percent || 0,
                    discount_amount: lineData.discount_amount || 0,
                    uom_id: lineData.uom_id,
                    required_date: lineData.required_date,
                    notes: lineData.notes
                }, { transaction });

                subtotal += (lineData.quantity_ordered * unitPrice) - (lineData.discount_amount || 0);
            }

            // Calculate taxes
            const taxAmount = await this.taxService.calculateTax(subtotal, soData.customer_id);
            const totalAmount = subtotal + taxAmount + (soData.shipping_amount || 0);

            // Update SO totals
            await so.update({
                subtotal,
                tax_amount: taxAmount,
                total_amount: totalAmount
            }, { transaction });

            await transaction.commit();

            return await this.getSOById(so.so_id);

        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    async reserveStock(soId, userId) {
        const transaction = await sequelize.transaction();

        try {
            const so = await SalesOrder.findByPk(soId, {
                include: [SalesOrderLine],
                transaction
            });

            if (!so) {
                throw new ValidationError('Sales order not found');
            }

            for (const line of so.SalesOrderLines) {
                const reservationResult = await this.reserveLineStock(line, transaction);

                await line.update({
                    quantity_reserved: reservationResult.quantity_reserved
                }, { transaction });
            }

            await so.update({ status: 'RESERVED' }, { transaction });
            await transaction.commit();

            return { success: true, message: 'Stock reserved successfully' };

        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    async reserveLineStock(soLine, transaction) {
        // Implement FEFO (First Expired, First Out) logic for items with expiry
        const item = await Item.findByPk(soLine.item_id);

        if (item.has_expiry) {
            return await this.reserveWithFEFO(soLine, transaction);
        } else {
            return await this.reserveWithFIFO(soLine, transaction);
        }
    }

    async reserveWithFEFO(soLine, transaction) {
        // Get available balances ordered by expiry date
        const balances = await InventoryBalance.findAll({
            where: {
                item_id: soLine.item_id,
                quantity_available: { [Op.gt]: 0 }
            },
            include: [{
                model: Lot,
                order: [['expiry_date', 'ASC']]
            }],
            transaction
        });

        let remainingToReserve = soLine.quantity_ordered;
        let totalReserved = 0;

        for (const balance of balances) {
            if (remainingToReserve <= 0) break;

            const reserveQuantity = Math.min(remainingToReserve, balance.quantity_available);

            // Create reservation transaction
            await this.inventoryService.processInventoryTransaction({
                item_id: soLine.item_id,
                location_id: balance.location_id,
                lot_id: balance.lot_id,
                transaction_type: 'RESERVATION',
                reference_type: 'SO',
                reference_id: soLine.so_id,
                quantity: reserveQuantity,
                transaction_date: new Date()
            }, transaction);

            totalReserved += reserveQuantity;
            remainingToReserve -= reserveQuantity;
        }

        return { quantity_reserved: totalReserved };
    }
}

module.exports = SalesOrderService;
```

## 6. Replenishment & Planning Logic

### Database Schema Requirements

#### REORDER_RULES Table
```sql
CREATE TABLE reorder_rules (
    rule_id BIGSERIAL PRIMARY KEY,
    item_id BIGINT NOT NULL REFERENCES items(item_id),
    location_id BIGINT REFERENCES locations(location_id),
    reorder_point DECIMAL(15,4) NOT NULL,
    reorder_quantity DECIMAL(15,4) NOT NULL,
    safety_stock DECIMAL(15,4) DEFAULT 0,
    lead_time_days INTEGER DEFAULT 0,
    review_cycle_days INTEGER DEFAULT 30,
    min_order_quantity DECIMAL(15,4) DEFAULT 0,
    max_order_quantity DECIMAL(15,4),
    supplier_id BIGINT REFERENCES suppliers(supplier_id),
    is_active BOOLEAN DEFAULT true,
    last_review_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(item_id, location_id)
);
```

### Replenishment Service Implementation

```javascript
// services/ReplenishmentService.js
const { ReorderRule, Item, InventoryBalance, InventoryTransaction } = require('../models');
const PurchaseOrderService = require('./PurchaseOrderService');

class ReplenishmentService {
    constructor() {
        this.purchaseOrderService = new PurchaseOrderService();
    }

    async calculateReorderPoints() {
        const rules = await ReorderRule.findAll({
            where: { is_active: true },
            include: [Item, Location]
        });

        for (const rule of rules) {
            const demandData = await this.calculateDemandMetrics(rule.item_id, rule.location_id);
            const newReorderPoint = await this.calculateOptimalReorderPoint(rule, demandData);

            await rule.update({
                reorder_point: newReorderPoint,
                last_review_date: new Date()
            });
        }
    }

    async calculateDemandMetrics(itemId, locationId) {
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

        const transactions = await InventoryTransaction.findAll({
            where: {
                item_id: itemId,
                location_id: locationId,
                transaction_type: 'ISSUE',
                transaction_date: { [Op.gte]: thirtyDaysAgo }
            },
            order: [['transaction_date', 'ASC']]
        });

        const totalDemand = transactions.reduce((sum, t) => sum + t.quantity, 0);
        const averageDailyDemand = totalDemand / 30;

        // Calculate demand variability (standard deviation)
        const dailyDemands = this.groupDemandByDay(transactions);
        const variance = this.calculateVariance(dailyDemands, averageDailyDemand);
        const standardDeviation = Math.sqrt(variance);

        return {
            averageDailyDemand,
            standardDeviation,
            totalDemand
        };
    }

    async calculateOptimalReorderPoint(rule, demandData) {
        const { averageDailyDemand, standardDeviation } = demandData;
        const leadTimeDemand = averageDailyDemand * rule.lead_time_days;

        // Safety stock calculation using service level (95% = 1.65 z-score)
        const serviceLevel = 1.65;
        const safetyStock = serviceLevel * standardDeviation * Math.sqrt(rule.lead_time_days);

        return leadTimeDemand + safetyStock;
    }

    async checkLowStockItems() {
        const lowStockItems = await this.findLowStockItems();

        for (const item of lowStockItems) {
            await this.generateReplenishmentRecommendation(item);
        }

        return lowStockItems;
    }

    async findLowStockItems() {
        return await InventoryBalance.findAll({
            include: [{
                model: ReorderRule,
                where: { is_active: true }
            }, {
                model: Item,
                where: { status: 'ACTIVE' }
            }],
            where: sequelize.where(
                sequelize.col('quantity_available'),
                Op.lte,
                sequelize.col('ReorderRule.reorder_point')
            )
        });
    }

    async generateReplenishmentRecommendation(balance) {
        const rule = balance.ReorderRule;
        const item = balance.Item;

        // Calculate EOQ (Economic Order Quantity)
        const eoq = await this.calculateEOQ(item, rule);
        const recommendedQuantity = Math.max(eoq, rule.reorder_quantity);

        // Create replenishment recommendation
        return {
            item_id: item.item_id,
            location_id: balance.location_id,
            current_stock: balance.quantity_available,
            reorder_point: rule.reorder_point,
            recommended_quantity: recommendedQuantity,
            supplier_id: rule.supplier_id,
            urgency: this.calculateUrgency(balance.quantity_available, rule.reorder_point)
        };
    }

    async calculateEOQ(item, rule) {
        // Economic Order Quantity formula: sqrt((2 * D * S) / H)
        // D = Annual demand, S = Setup cost, H = Holding cost

        const annualDemand = await this.getAnnualDemand(item.item_id);
        const setupCost = 100; // Default setup cost
        const holdingCostRate = 0.25; // 25% of item cost
        const holdingCost = item.standard_cost * holdingCostRate;

        if (holdingCost <= 0) return rule.reorder_quantity;

        return Math.sqrt((2 * annualDemand * setupCost) / holdingCost);
    }
}

module.exports = ReplenishmentService;
```

## 7. Warehouse Management Logic

### Database Schema Requirements

#### LOCATIONS Table
```sql
CREATE TABLE locations (
    location_id BIGSERIAL PRIMARY KEY,
    location_code VARCHAR(20) UNIQUE NOT NULL,
    location_name VARCHAR(100) NOT NULL,
    location_type VARCHAR(20) DEFAULT 'WAREHOUSE', -- WAREHOUSE, STORE, TRANSIT
    parent_location_id BIGINT REFERENCES locations(location_id),
    address JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### STORAGE_BINS Table
```sql
CREATE TABLE storage_bins (
    bin_id BIGSERIAL PRIMARY KEY,
    location_id BIGINT NOT NULL REFERENCES locations(location_id),
    bin_code VARCHAR(20) NOT NULL,
    bin_type VARCHAR(20) DEFAULT 'STANDARD', -- STANDARD, BULK, PICKING, RECEIVING
    zone VARCHAR(20),
    aisle VARCHAR(10),
    rack VARCHAR(10),
    shelf VARCHAR(10),
    position VARCHAR(10),
    capacity_volume DECIMAL(10,4),
    capacity_weight DECIMAL(10,4),
    current_volume DECIMAL(10,4) DEFAULT 0,
    current_weight DECIMAL(10,4) DEFAULT 0,
    temperature_controlled BOOLEAN DEFAULT false,
    min_temperature DECIMAL(5,2),
    max_temperature DECIMAL(5,2),
    is_active BOOLEAN DEFAULT true,
    UNIQUE(location_id, bin_code)
);
```

### Warehouse Service Implementation

```javascript
// services/WarehouseService.js
const { Location, StorageBin, InventoryBalance, Item } = require('../models');
const { ValidationError, BusinessLogicError } = require('../utils/errors');

class WarehouseService {
    async assignOptimalBin(itemId, locationId, quantity) {
        const item = await Item.findByPk(itemId);
        if (!item) {
            throw new ValidationError('Item not found');
        }

        // Get available bins based on item requirements
        const availableBins = await this.getAvailableBins(locationId, item, quantity);

        if (availableBins.length === 0) {
            throw new BusinessLogicError('No suitable bins available');
        }

        // Score bins based on optimization criteria
        const scoredBins = await this.scoreBins(availableBins, item, quantity);

        // Return the best bin
        return scoredBins[0];
    }

    async getAvailableBins(locationId, item, quantity) {
        const requiredVolume = quantity * (item.volume || 0);
        const requiredWeight = quantity * (item.weight || 0);

        const whereClause = {
            location_id: locationId,
            is_active: true
        };

        // Add capacity constraints
        if (requiredVolume > 0) {
            whereClause.capacity_volume = { [Op.gte]: sequelize.literal('current_volume + ' + requiredVolume) };
        }
        if (requiredWeight > 0) {
            whereClause.capacity_weight = { [Op.gte]: sequelize.literal('current_weight + ' + requiredWeight) };
        }

        // Add temperature requirements
        if (item.storage_requirements?.temperature) {
            whereClause.temperature_controlled = true;
            whereClause.min_temperature = { [Op.lte]: item.storage_requirements.temperature };
            whereClause.max_temperature = { [Op.gte]: item.storage_requirements.temperature };
        }

        return await StorageBin.findAll({ where: whereClause });
    }

    async scoreBins(bins, item, quantity) {
        const scoredBins = [];

        for (const bin of bins) {
            let score = 0;

            // Prefer bins with existing inventory of same item (consolidation)
            const existingBalance = await InventoryBalance.findOne({
                where: { item_id: item.item_id, bin_id: bin.bin_id }
            });
            if (existingBalance) score += 50;

            // Prefer bins in picking zones for fast-moving items
            if (item.movement_type === 'FAST' && bin.zone === 'PICKING') score += 30;

            // Prefer bins closer to shipping for A-class items
            if (item.abc_classification === 'A' && bin.zone === 'SHIPPING') score += 20;

            // Capacity utilization score (prefer bins that will be well-utilized)
            const volumeUtilization = (bin.current_volume + (quantity * item.volume)) / bin.capacity_volume;
            if (volumeUtilization >= 0.7 && volumeUtilization <= 0.9) score += 25;

            scoredBins.push({ bin, score });
        }

        return scoredBins.sort((a, b) => b.score - a.score).map(s => s.bin);
    }

    async processTransfer(transferData, userId) {
        const transaction = await sequelize.transaction();

        try {
            const {
                item_id,
                from_location_id,
                to_location_id,
                quantity,
                lot_id,
                serial_number
            } = transferData;

            // Validate source availability
            const sourceBalance = await InventoryBalance.findOne({
                where: {
                    item_id,
                    location_id: from_location_id,
                    lot_id: lot_id || null,
                    serial_number: serial_number || null
                },
                transaction
            });

            if (!sourceBalance || sourceBalance.quantity_available < quantity) {
                throw new BusinessLogicError('Insufficient inventory at source location');
            }

            // Create transfer out transaction
            await this.inventoryService.processInventoryTransaction({
                item_id,
                location_id: from_location_id,
                lot_id,
                serial_number,
                transaction_type: 'ISSUE',
                transaction_subtype: 'TRANSFER_OUT',
                reference_type: 'TRANSFER',
                reference_id: transferData.transfer_id,
                quantity,
                transaction_date: new Date()
            }, userId, transaction);

            // Create transfer in transaction
            await this.inventoryService.processInventoryTransaction({
                item_id,
                location_id: to_location_id,
                lot_id,
                serial_number,
                transaction_type: 'RECEIPT',
                transaction_subtype: 'TRANSFER_IN',
                reference_type: 'TRANSFER',
                reference_id: transferData.transfer_id,
                quantity,
                unit_cost: sourceBalance.unit_cost,
                transaction_date: new Date()
            }, userId, transaction);

            await transaction.commit();

            return { success: true, message: 'Transfer completed successfully' };

        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    async generatePickList(salesOrderIds) {
        const pickItems = [];

        for (const soId of salesOrderIds) {
            const so = await SalesOrder.findByPk(soId, {
                include: [{
                    model: SalesOrderLine,
                    include: [Item]
                }]
            });

            for (const line of so.SalesOrderLines) {
                const allocations = await this.getAllocationsForLine(line);
                pickItems.push(...allocations);
            }
        }

        // Optimize pick sequence
        const optimizedPickList = await this.optimizePickSequence(pickItems);

        return {
            pick_list_id: await this.generatePickListNumber(),
            items: optimizedPickList,
            total_items: optimizedPickList.length,
            estimated_time: this.calculatePickTime(optimizedPickList)
        };
    }

    async optimizePickSequence(pickItems) {
        // Group by zone and sort by bin location
        const groupedByZone = pickItems.reduce((groups, item) => {
            const zone = item.bin.zone || 'DEFAULT';
            if (!groups[zone]) groups[zone] = [];
            groups[zone].push(item);
            return groups;
        }, {});

        // Sort within each zone by aisle, rack, shelf
        for (const zone in groupedByZone) {
            groupedByZone[zone].sort((a, b) => {
                if (a.bin.aisle !== b.bin.aisle) return a.bin.aisle.localeCompare(b.bin.aisle);
                if (a.bin.rack !== b.bin.rack) return a.bin.rack.localeCompare(b.bin.rack);
                return a.bin.shelf.localeCompare(b.bin.shelf);
            });
        }

        // Combine zones in optimal order (e.g., BULK -> PICKING -> SHIPPING)
        const zoneOrder = ['BULK', 'PICKING', 'SHIPPING', 'DEFAULT'];
        const optimizedList = [];

        for (const zone of zoneOrder) {
            if (groupedByZone[zone]) {
                optimizedList.push(...groupedByZone[zone]);
            }
        }

        return optimizedList;
    }
}

module.exports = WarehouseService;
```

## 8. Background Job Processing & Queue Management

### Queue Service Implementation

```javascript
// services/QueueService.js
const Queue = require('bull');
const Redis = require('redis');

class QueueService {
    constructor() {
        this.redis = Redis.createClient(process.env.REDIS_URL);
        this.queues = {
            inventory: new Queue('inventory processing', process.env.REDIS_URL),
            reports: new Queue('report generation', process.env.REDIS_URL),
            notifications: new Queue('notifications', process.env.REDIS_URL),
            integrations: new Queue('external integrations', process.env.REDIS_URL)
        };

        this.setupProcessors();
    }

    setupProcessors() {
        // Inventory processing jobs
        this.queues.inventory.process('reorder-check', 5, require('../jobs/ReorderCheckJob'));
        this.queues.inventory.process('abc-classification', 1, require('../jobs/ABCClassificationJob'));
        this.queues.inventory.process('cost-revaluation', 1, require('../jobs/CostRevaluationJob'));

        // Report generation jobs
        this.queues.reports.process('inventory-report', 3, require('../jobs/InventoryReportJob'));
        this.queues.reports.process('sales-report', 3, require('../jobs/SalesReportJob'));

        // Notification jobs
        this.queues.notifications.process('email', 10, require('../jobs/EmailNotificationJob'));
        this.queues.notifications.process('sms', 5, require('../jobs/SMSNotificationJob'));
    }

    async addJob(queueName, jobType, data, options = {}) {
        const queue = this.queues[queueName];
        if (!queue) {
            throw new Error(`Queue ${queueName} not found`);
        }

        return await queue.add(jobType, data, {
            attempts: options.attempts || 3,
            backoff: options.backoff || 'exponential',
            delay: options.delay || 0,
            priority: options.priority || 0,
            ...options
        });
    }

    async scheduleRecurringJobs() {
        // Daily reorder point checks
        await this.queues.inventory.add('reorder-check', {}, {
            repeat: { cron: '0 6 * * *' }, // 6 AM daily
            removeOnComplete: 5,
            removeOnFail: 10
        });

        // Weekly ABC classification
        await this.queues.inventory.add('abc-classification', {}, {
            repeat: { cron: '0 2 * * 0' }, // 2 AM every Sunday
            removeOnComplete: 2,
            removeOnFail: 5
        });

        // Monthly cost revaluation
        await this.queues.inventory.add('cost-revaluation', {}, {
            repeat: { cron: '0 1 1 * *' }, // 1 AM on 1st of each month
            removeOnComplete: 1,
            removeOnFail: 3
        });
    }
}

module.exports = QueueService;
```

## 9. Error Handling & Recovery

### Error Service Implementation

```javascript
// services/ErrorService.js
const winston = require('winston');

class ErrorService {
    constructor() {
        this.logger = winston.createLogger({
            level: 'info',
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.errors({ stack: true }),
                winston.format.json()
            ),
            transports: [
                new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
                new winston.transports.File({ filename: 'logs/combined.log' }),
                new winston.transports.Console({
                    format: winston.format.simple()
                })
            ]
        });
    }

    handleError(error, context = {}) {
        const errorInfo = {
            message: error.message,
            stack: error.stack,
            type: error.constructor.name,
            context,
            timestamp: new Date().toISOString()
        };

        // Log error
        this.logger.error('Application Error', errorInfo);

        // Send alerts for critical errors
        if (this.isCriticalError(error)) {
            this.sendCriticalErrorAlert(errorInfo);
        }

        // Return sanitized error for client
        return this.sanitizeError(error);
    }

    isCriticalError(error) {
        const criticalTypes = [
            'DatabaseConnectionError',
            'PaymentProcessingError',
            'SecurityViolationError'
        ];

        return criticalTypes.includes(error.constructor.name) ||
               error.message.includes('CRITICAL');
    }

    sanitizeError(error) {
        // Don't expose internal details to client
        if (process.env.NODE_ENV === 'production') {
            return {
                message: 'An error occurred. Please try again.',
                code: error.code || 'INTERNAL_ERROR'
            };
        }

        return {
            message: error.message,
            code: error.code || 'INTERNAL_ERROR',
            stack: error.stack
        };
    }
}

module.exports = ErrorService;
```

## 10. Performance Optimization

### Caching Service Implementation

```javascript
// services/CacheService.js
const Redis = require('redis');

class CacheService {
    constructor() {
        this.redis = Redis.createClient(process.env.REDIS_URL);
        this.defaultTTL = 3600; // 1 hour
    }

    async get(key) {
        try {
            const value = await this.redis.get(key);
            return value ? JSON.parse(value) : null;
        } catch (error) {
            console.error('Cache get error:', error);
            return null;
        }
    }

    async set(key, value, ttl = this.defaultTTL) {
        try {
            await this.redis.setex(key, ttl, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('Cache set error:', error);
            return false;
        }
    }

    async del(key) {
        try {
            await this.redis.del(key);
            return true;
        } catch (error) {
            console.error('Cache delete error:', error);
            return false;
        }
    }

    async invalidatePattern(pattern) {
        try {
            const keys = await this.redis.keys(pattern);
            if (keys.length > 0) {
                await this.redis.del(...keys);
            }
            return true;
        } catch (error) {
            console.error('Cache invalidation error:', error);
            return false;
        }
    }

    // Cache frequently accessed data
    async cacheItemMaster(itemId, itemData) {
        await this.set(`item:${itemId}`, itemData, 7200); // 2 hours
    }

    async cacheInventoryBalance(itemId, locationId, balance) {
        await this.set(`balance:${itemId}:${locationId}`, balance, 300); // 5 minutes
    }

    async cachePricing(itemId, customerId, pricing) {
        await this.set(`price:${itemId}:${customerId}`, pricing, 1800); // 30 minutes
    }
}

module.exports = CacheService;
```

## 11. Integration & API Logic

### API Gateway Implementation

```javascript
// middleware/apiGateway.js
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');

const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP'
});

const apiSecurity = helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"]
        }
    }
});

module.exports = { apiLimiter, apiSecurity };
```

### External Integration Service

```javascript
// services/IntegrationService.js
const axios = require('axios');

class IntegrationService {
    constructor() {
        this.integrations = {
            accounting: {
                url: process.env.ACCOUNTING_API_URL,
                apiKey: process.env.ACCOUNTING_API_KEY
            },
            ecommerce: {
                url: process.env.ECOMMERCE_API_URL,
                apiKey: process.env.ECOMMERCE_API_KEY
            },
            shipping: {
                url: process.env.SHIPPING_API_URL,
                apiKey: process.env.SHIPPING_API_KEY
            }
        };
    }

    async syncToAccounting(transactionData) {
        try {
            const response = await axios.post(
                `${this.integrations.accounting.url}/transactions`,
                transactionData,
                {
                    headers: {
                        'Authorization': `Bearer ${this.integrations.accounting.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: 30000
                }
            );

            return response.data;
        } catch (error) {
            console.error('Accounting sync error:', error);
            throw new Error('Failed to sync with accounting system');
        }
    }

    async updateEcommerceInventory(itemId, quantity) {
        try {
            await axios.put(
                `${this.integrations.ecommerce.url}/inventory/${itemId}`,
                { quantity },
                {
                    headers: {
                        'Authorization': `Bearer ${this.integrations.ecommerce.apiKey}`
                    }
                }
            );
        } catch (error) {
            console.error('E-commerce sync error:', error);
            // Don't throw - this is non-critical
        }
    }
}

module.exports = IntegrationService;
```

## 12. Testing Strategy

### Unit Test Example

```javascript
// tests/services/ItemService.test.js
const { expect } = require('chai');
const sinon = require('sinon');
const ItemService = require('../../services/ItemService');
const { Item, Category } = require('../../models');

describe('ItemService', () => {
    let itemService;
    let sandbox;

    beforeEach(() => {
        sandbox = sinon.createSandbox();
        itemService = new ItemService();
    });

    afterEach(() => {
        sandbox.restore();
    });

    describe('createItem', () => {
        it('should create item with valid data', async () => {
            const itemData = {
                sku: 'TEST001',
                item_name: 'Test Item',
                category_id: 1,
                base_uom_id: 1
            };

            sandbox.stub(Item, 'findOne').resolves(null);
            sandbox.stub(Category, 'findByPk').resolves({ category_id: 1 });
            sandbox.stub(Item, 'create').resolves({ item_id: 1, ...itemData });

            const result = await itemService.createItem(itemData, 1);

            expect(result).to.have.property('item_id');
            expect(result.sku).to.equal('TEST001');
        });

        it('should throw error for duplicate SKU', async () => {
            const itemData = {
                sku: 'DUPLICATE',
                item_name: 'Test Item',
                category_id: 1,
                base_uom_id: 1
            };

            sandbox.stub(Item, 'findOne').resolves({ item_id: 1 });

            try {
                await itemService.createItem(itemData, 1);
                expect.fail('Should have thrown error');
            } catch (error) {
                expect(error.message).to.include('SKU already exists');
            }
        });
    });
});
```

### Integration Test Example

```javascript
// tests/integration/inventory.test.js
const request = require('supertest');
const app = require('../../app');
const { sequelize } = require('../../models');

describe('Inventory API Integration', () => {
    let authToken;

    before(async () => {
        await sequelize.sync({ force: true });

        // Create test user and get auth token
        const loginResponse = await request(app)
            .post('/api/auth/login')
            .send({
                email: '<EMAIL>',
                password: 'password123'
            });

        authToken = loginResponse.body.tokens.accessToken;
    });

    describe('POST /api/inventory/transactions', () => {
        it('should process inventory receipt', async () => {
            const transactionData = {
                item_id: 1,
                location_id: 1,
                transaction_type: 'RECEIPT',
                quantity: 100,
                unit_cost: 10.50
            };

            const response = await request(app)
                .post('/api/inventory/transactions')
                .set('Authorization', `Bearer ${authToken}`)
                .send(transactionData)
                .expect(201);

            expect(response.body.success).to.be.true;
            expect(response.body.data).to.have.property('transaction_id');
        });
    });
});
```

## 13. Deployment & Monitoring

### Docker Configuration

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["npm", "start"]
```

### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**********************************/inventory
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=inventory
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### Monitoring Setup

```javascript
// monitoring/metrics.js
const prometheus = require('prom-client');

// Create metrics
const httpRequestDuration = new prometheus.Histogram({
    name: 'http_request_duration_seconds',
    help: 'Duration of HTTP requests in seconds',
    labelNames: ['method', 'route', 'status_code']
});

const inventoryTransactions = new prometheus.Counter({
    name: 'inventory_transactions_total',
    help: 'Total number of inventory transactions',
    labelNames: ['type', 'status']
});

const activeUsers = new prometheus.Gauge({
    name: 'active_users',
    help: 'Number of active users'
});

// Middleware to collect metrics
const collectMetrics = (req, res, next) => {
    const start = Date.now();

    res.on('finish', () => {
        const duration = (Date.now() - start) / 1000;
        httpRequestDuration
            .labels(req.method, req.route?.path || req.path, res.statusCode)
            .observe(duration);
    });

    next();
};

module.exports = {
    httpRequestDuration,
    inventoryTransactions,
    activeUsers,
    collectMetrics,
    register: prometheus.register
};
```

## 10. Reporting & Analytics Logic

### Real-Time Reporting
- **Dashboard Data Aggregation**: Real-time KPI calculations
- **Stock Level Monitoring**: Live inventory status updates
- **Alert Generation**: Low stock and expiry warnings
- **Performance Metrics**: Calculate turnover and velocity metrics

### Business Intelligence
- **Sales Analysis**: Revenue and margin analysis
- **Inventory Analysis**: ABC analysis and aging reports
- **Supplier Performance**: Delivery and quality metrics
- **Customer Analysis**: Purchase patterns and profitability

## 11. Integration & API Logic

### External System Integration
- **POS System Sync**: Real-time sales data integration
- **Accounting System Integration**: GL posting and reconciliation
- **E-commerce Platform Sync**: Multi-channel inventory updates
- **Supplier Portal Integration**: Direct supplier interactions
- **Shipping API Integration**: Tracking and delivery updates

### Data Synchronization
- **Conflict Resolution**: Handle data conflicts during sync
- **Batch Processing**: Efficient bulk data operations
- **Error Handling**: Robust error recovery mechanisms
- **Audit Trail Maintenance**: Log all integration activities

## 12. Background Job Processing

### Scheduled Tasks
- **Inventory Revaluation**: Periodic cost recalculations
- **Expiry Date Monitoring**: Daily expiry checks and alerts
- **Reorder Point Monitoring**: Continuous stock level monitoring
- **Data Cleanup**: Archive old transactions and logs
- **Report Generation**: Automated report creation and distribution

### Queue Management
- **Job Prioritization**: Handle critical tasks first
- **Retry Logic**: Handle failed job retries
- **Dead Letter Queue**: Manage permanently failed jobs
- **Performance Monitoring**: Track job execution times

## 13. Data Validation & Business Rules

### Input Validation
- **Data Type Validation**: Ensure correct data formats
- **Business Rule Validation**: Enforce business constraints
- **Cross-Reference Validation**: Validate foreign key relationships
- **Range Validation**: Ensure values within acceptable limits

### Business Logic Enforcement
- **Workflow Validation**: Ensure proper process sequences
- **Status Transition Rules**: Valid status change workflows
- **Approval Requirements**: Enforce approval hierarchies
- **Document Numbering**: Auto-generate sequential numbers

## 14. Audit & Compliance Logic

### Audit Trail Management
- **Change Tracking**: Log all data modifications
- **User Activity Logging**: Track user actions and access
- **Document Versioning**: Maintain historical versions
- **Compliance Reporting**: Generate regulatory reports

### Data Integrity
- **Referential Integrity**: Maintain database consistency
- **Transaction Atomicity**: Ensure all-or-nothing operations
- **Concurrency Control**: Handle simultaneous user access
- **Backup Verification**: Validate data backup integrity

## 15. Performance Optimization Logic

### Caching Strategies
- **Frequently Accessed Data**: Cache item masters and prices
- **Query Result Caching**: Store expensive query results
- **Session Caching**: Maintain user session data
- **Cache Invalidation**: Smart cache refresh strategies

### Database Optimization
- **Query Optimization**: Efficient database queries
- **Index Management**: Maintain optimal database indexes
- **Connection Pooling**: Efficient database connection management
- **Partitioning Logic**: Partition large tables for performance

## 16. Error Handling & Recovery

### Exception Management
- **Graceful Error Handling**: User-friendly error messages
- **Error Logging**: Comprehensive error tracking
- **Recovery Procedures**: Automatic error recovery where possible
- **Escalation Logic**: Alert administrators for critical errors

### Data Recovery
- **Transaction Rollback**: Undo incomplete transactions
- **Data Restoration**: Restore from backups when needed
- **Consistency Checks**: Validate data integrity after recovery
- **Failover Logic**: Switch to backup systems during outages

## 17. Security & Data Protection

### Data Security
- **Data Encryption**: Encrypt sensitive data at rest and in transit
- **SQL Injection Prevention**: Parameterized queries and validation
- **XSS Protection**: Sanitize user inputs
- **CSRF Protection**: Validate request authenticity

### Access Control
- **IP Whitelisting**: Restrict access by IP address
- **Rate Limiting**: Prevent API abuse
- **Privilege Escalation Prevention**: Validate permission changes
- **Secure File Upload**: Validate and sanitize uploaded files

This comprehensive server-side logic framework ensures robust, secure, and efficient operation of the inventory management system while maintaining data integrity and supporting complex business processes.