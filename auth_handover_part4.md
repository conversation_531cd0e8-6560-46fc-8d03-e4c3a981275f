# Developer Handover Document: Authentication & Authorization Services (Part 4)

*Continuation from Part 3...*

| `/auth/mfa/setup` | POST | Yes | Setup MFA for user account |
| `/auth/mfa/verify` | POST | No | Verify MFA token during login |
| `/auth/mfa/disable` | POST | Yes | Disable MFA for user account |
| `/auth/password/reset-request` | POST | No | Request password reset |
| `/auth/password/reset` | POST | No | Reset password with token |
| `/auth/password/change` | POST | Yes | Change password for authenticated user |

### Authorization Endpoints Summary

| Endpoint | Method | Auth Required | Description |
|----------|--------|---------------|-------------|
| `/auth/permissions/check` | POST | Yes | Check specific permissions |
| `/auth/permissions/user/{userId}` | GET | Yes | Get user permissions |
| `/auth/roles/user/{userId}` | GET | Yes | Get user roles |
| `/auth/roles/assign` | POST | Yes | Assign role to user |
| `/auth/roles/revoke` | POST | Yes | Revoke role from user |

## Advanced Security Features

### IP-Based Access Control

```javascript
// services/IPSecurityService.js
const { Op } = require('sequelize');

class IPSecurityService {
    constructor() {
        this.whitelistCache = new Map();
        this.blacklistCache = new Map();
        this.maxFailedAttempts = 10;
        this.blockDuration = 24 * 60 * 60 * 1000; // 24 hours
    }

    async checkIPAccess(ipAddress, userId = null) {
        // Check if IP is blacklisted
        if (await this.isIPBlacklisted(ipAddress)) {
            return {
                allowed: false,
                reason: 'IP_BLACKLISTED',
                blockExpiresAt: await this.getBlockExpiry(ipAddress)
            };
        }

        // Check rate limiting
        const rateLimitResult = await this.checkRateLimit(ipAddress);
        if (!rateLimitResult.allowed) {
            return rateLimitResult;
        }

        // Check geo-location restrictions (if enabled)
        if (userId && await this.hasGeoRestrictions(userId)) {
            const geoResult = await this.checkGeoRestriction(ipAddress, userId);
            if (!geoResult.allowed) {
                return geoResult;
            }
        }

        return { allowed: true };
    }

    async recordFailedAttempt(ipAddress, userId = null) {
        const key = `failed_ip:${ipAddress}`;
        const cache = new CacheService();
        
        let attempts = await cache.redis.get(key) || 0;
        attempts = parseInt(attempts) + 1;

        // Set expiry for 1 hour if not already blacklisted
        await cache.redis.setex(key, 3600, attempts);

        // Blacklist if too many attempts
        if (attempts >= this.maxFailedAttempts) {
            await this.blacklistIP(ipAddress, 'TOO_MANY_FAILED_ATTEMPTS');
            
            // Log security event
            const auditService = new AuditService();
            await auditService.logAuthEvent(userId, 'IP_BLACKLISTED', {
                ipAddress: ipAddress,
                failedAttempts: attempts,
                reason: 'Excessive failed login attempts'
            });
        }

        return attempts;
    }

    async blacklistIP(ipAddress, reason, duration = null) {
        const expiresAt = duration ? 
            new Date(Date.now() + duration) : 
            new Date(Date.now() + this.blockDuration);

        await IPBlacklist.create({
            ip_address: ipAddress,
            reason: reason,
            blocked_at: new Date(),
            expires_at: expiresAt,
            is_active: true
        });

        // Cache the blacklist status
        this.blacklistCache.set(ipAddress, expiresAt);
    }

    async isIPBlacklisted(ipAddress) {
        // Check cache first
        if (this.blacklistCache.has(ipAddress)) {
            const expiry = this.blacklistCache.get(ipAddress);
            if (expiry > new Date()) {
                return true;
            } else {
                this.blacklistCache.delete(ipAddress);
            }
        }

        // Check database
        const blacklistEntry = await IPBlacklist.findOne({
            where: {
                ip_address: ipAddress,
                is_active: true,
                expires_at: { [Op.gt]: new Date() }
            }
        });

        if (blacklistEntry) {
            this.blacklistCache.set(ipAddress, blacklistEntry.expires_at);
            return true;
        }

        return false;
    }

    async checkRateLimit(ipAddress) {
        const cache = new CacheService();
        const window = 15 * 60; // 15 minutes
        const maxRequests = 100;
        
        const key = `rate_limit:${ipAddress}`;
        const current = await cache.redis.get(key) || 0;
        
        if (parseInt(current) >= maxRequests) {
            return {
                allowed: false,
                reason: 'RATE_LIMIT_EXCEEDED',
                retryAfter: await cache.redis.ttl(key)
            };
        }

        // Increment counter
        await cache.redis.multi()
            .incr(key)
            .expire(key, window)
            .exec();

        return { allowed: true };
    }

    async getGeoLocation(ipAddress) {
        // Integration with GeoIP service (example with MaxMind)
        try {
            const response = await fetch(`https://api.maxmind.com/geoip/v2.1/city/${ipAddress}`, {
                headers: {
                    'Authorization': `Basic ${Buffer.from(`${process.env.MAXMIND_USER}:${process.env.MAXMIND_PASSWORD}`).toString('base64')}`
                }
            });
            
            if (response.ok) {
                return await response.json();
            }
            
            return null;
        } catch (error) {
            console.error('GeoIP lookup failed:', error);
            return null;
        }
    }

    async checkGeoRestriction(ipAddress, userId) {
        const geoData = await this.getGeoLocation(ipAddress);
        if (!geoData) {
            // If we can't determine location, allow access but log it
            return { allowed: true, warning: 'GEOLOCATION_UNAVAILABLE' };
        }

        const userRestrictions = await UserGeoRestriction.findAll({
            where: { user_id: userId, is_active: true }
        });

        if (userRestrictions.length === 0) {
            return { allowed: true };
        }

        const userCountry = geoData.country?.iso_code;
        const allowedCountries = userRestrictions
            .filter(r => r.restriction_type === 'ALLOW')
            .map(r => r.country_code);
        
        const blockedCountries = userRestrictions
            .filter(r => r.restriction_type === 'BLOCK')
            .map(r => r.country_code);

        // Check blocked countries first
        if (blockedCountries.includes(userCountry)) {
            return {
                allowed: false,
                reason: 'GEO_BLOCKED',
                country: userCountry
            };
        }

        // Check allowed countries (if any specified)
        if (allowedCountries.length > 0 && !allowedCountries.includes(userCountry)) {
            return {
                allowed: false,
                reason: 'GEO_NOT_ALLOWED',
                country: userCountry
            };
        }

        return { allowed: true };
    }
}

module.exports = IPSecurityService;
```

### Device Fingerprinting and Management

```javascript
// services/DeviceService.js
const crypto = require('crypto');

class DeviceService {
    constructor() {
        this.cache = new CacheService();
    }

    generateDeviceFingerprint(userAgent, acceptLanguage, timezone, screenResolution) {
        const data = `${userAgent}|${acceptLanguage}|${timezone}|${screenResolution}`;
        return crypto.createHash('sha256').update(data).digest('hex');
    }

    async registerDevice(userId, deviceInfo, ipAddress) {
        const fingerprint = this.generateDeviceFingerprint(
            deviceInfo.userAgent,
            deviceInfo.acceptLanguage,
            deviceInfo.timezone,
            deviceInfo.screenResolution
        );

        // Check if device already exists
        let device = await UserDevice.findOne({
            where: {
                user_id: userId,
                device_fingerprint: fingerprint
            }
        });

        if (device) {
            // Update last seen
            await device.update({
                last_seen_at: new Date(),
                last_ip_address: ipAddress,
                is_active: true
            });
        } else {
            // Create new device
            device = await UserDevice.create({
                user_id: userId,
                device_fingerprint: fingerprint,
                device_name: this.getDeviceName(deviceInfo.userAgent),
                user_agent: deviceInfo.userAgent,
                first_seen_at: new Date(),
                last_seen_at: new Date(),
                last_ip_address: ipAddress,
                is_trusted: false,
                is_active: true
            });

            // Send new device notification
            await this.sendNewDeviceNotification(userId, device, ipAddress);
        }

        return device;
    }

    async trustDevice(userId, deviceId, trustedBy) {
        const device = await UserDevice.findOne({
            where: {
                device_id: deviceId,
                user_id: userId
            }
        });

        if (!device) {
            throw new Error('Device not found');
        }

        await device.update({ 
            is_trusted: true,
            trusted_at: new Date(),
            trusted_by: trustedBy
        });

        // Log the event
        const auditService = new AuditService();
        await auditService.logAuthEvent(userId, 'DEVICE_TRUSTED', {
            deviceId: deviceId,
            deviceName: device.device_name,
            trustedBy: trustedBy
        });

        return device;
    }

    async revokeDevice(userId, deviceId, revokedBy) {
        const device = await UserDevice.findOne({
            where: {
                device_id: deviceId,
                user_id: userId
            }
        });

        if (!device) {
            throw new Error('Device not found');
        }

        await device.update({ 
            is_active: false,
            revoked_at: new Date(),
            revoked_by: revokedBy
        });

        // Invalidate all sessions for this device
        await UserSession.update(
            { is_active: false },
            {
                where: {
                    user_id: userId,
                    device_fingerprint: device.device_fingerprint
                }
            }
        );

        // Log the event
        const auditService = new AuditService();
        await auditService.logAuthEvent(userId, 'DEVICE_REVOKED', {
            deviceId: deviceId,
            deviceName: device.device_name,
            revokedBy: revokedBy
        });

        return device;
    }

    async getUserDevices(userId) {
        return await UserDevice.findAll({
            where: {
                user_id: userId,
                is_active: true
            },
            order: [['last_seen_at', 'DESC']]
        });
    }

    async checkDeviceRisk(userId, deviceFingerprint, ipAddress) {
        const device = await UserDevice.findOne({
            where: {
                user_id: userId,
                device_fingerprint: deviceFingerprint
            }
        });

        let riskLevel = 'LOW';
        let riskFactors = [];

        if (!device) {
            riskLevel = 'HIGH';
            riskFactors.push('NEW_DEVICE');
        } else {
            // Check for suspicious activity
            const daysSinceLastSeen = Math.floor(
                (Date.now() - device.last_seen_at.getTime()) / (1000 * 60 * 60 * 24)
            );

            if (daysSinceLastSeen > 30) {
                riskLevel = 'MEDIUM';
                riskFactors.push('LONG_ABSENCE');
            }

            if (device.last_ip_address !== ipAddress) {
                riskLevel = riskLevel === 'HIGH' ? 'HIGH' : 'MEDIUM';
                riskFactors.push('IP_CHANGE');
            }

            if (!device.is_trusted) {
                riskLevel = riskLevel === 'HIGH' ? 'HIGH' : 'MEDIUM';
                riskFactors.push('UNTRUSTED_DEVICE');
            }
        }

        return {
            riskLevel,
            riskFactors,
            device: device
        };
    }

    getDeviceName(userAgent) {
        // Simple device name extraction from user agent
        if (userAgent.includes('iPhone')) return 'iPhone';
        if (userAgent.includes('iPad')) return 'iPad';
        if (userAgent.includes('Android')) return 'Android Device';
        if (userAgent.includes('Windows')) return 'Windows Computer';
        if (userAgent.includes('Macintosh')) return 'Mac Computer';
        if (userAgent.includes('Linux')) return 'Linux Computer';
        return 'Unknown Device';
    }

    async sendNewDeviceNotification(userId, device, ipAddress) {
        // Implementation would depend on your notification service
        const user = await User.findByPk(userId);
        if (!user) return;

        const notificationService = require('./NotificationService');
        await notificationService.sendEmail(user.email, 'new-device', {
            deviceName: device.device_name,
            ipAddress: ipAddress,
            timestamp: device.first_seen_at,
            loginUrl: `${process.env.FRONTEND_URL}/login`
        });
    }
}

module.exports = DeviceService;
```

### Advanced MFA Implementation

```javascript
// services/MFAService.js
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const crypto = require('crypto');

class MFAService {
    constructor() {
        this.cache = new CacheService();
    }

    async setupTOTP(userId) {
        const user = await User.findByPk(userId);
        if (!user) {
            throw new Error('User not found');
        }

        // Generate secret
        const secret = speakeasy.generateSecret({
            name: `${user.email}`,
            issuer: process.env.APP_NAME || 'Inventory Management',
            length: 32
        });

        // Store temporary secret (not activated yet)
        await this.cache.redis.setex(
            `mfa_setup:${userId}`, 
            600, // 10 minutes
            secret.base32
        );

        // Generate QR code
        const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

        return {
            secret: secret.base32,
            qrCode: qrCodeUrl,
            manualEntryKey: secret.base32
        };
    }

    async confirmTOTPSetup(userId, token) {
        const tempSecret = await this.cache.redis.get(`mfa_setup:${userId}`);
        if (!tempSecret) {
            throw new Error('Setup session expired');
        }

        // Verify the token
        const verified = speakeasy.totp.verify({
            secret: tempSecret,
            encoding: 'base32',
            token: token,
            window: 2
        });

        if (!verified) {
            throw new Error('Invalid verification code');
        }

        // Save the secret to database
        await User.update(
            {
                mfa_enabled: true,
                mfa_secret: tempSecret,
                mfa_backup_codes: this.generateBackupCodes()
            },
            { where: { user_id: userId } }
        );

        // Clean up temporary secret
        await this.cache.redis.del(`mfa_setup:${userId}`);

        // Log the event
        const auditService = new AuditService();
        await auditService.logAuthEvent(userId, 'MFA_ENABLED', {
            method: 'TOTP'
        });

        return { success: true };
    }

    async verifyTOTP(userId, token) {
        const user = await User.findByPk(userId);
        if (!user || !user.mfa_enabled || !user.mfa_secret) {
            throw new Error('MFA not enabled for user');
        }

        // Check if it's a backup code
        if (token.length === 8 && /^\d+$/.test(token)) {
            return await this.verifyBackupCode(userId, token);
        }

        // Verify TOTP token
        const verified = speakeasy.totp.verify({
            secret: user.mfa_secret,
            encoding: 'base32',
            token: token,
            window: 2
        });

        if (verified) {
            // Log successful MFA verification
            const auditService = new AuditService();
            await auditService.logAuthEvent(userId, 'MFA_VERIFIED', {
                method: 'TOTP'
            });
        }

        return verified;
    }

    async verifyBackupCode(userId, code) {
        const user = await User.findByPk(userId);
        if (!user || !user.mfa_backup_codes) {
            return false;
        }

        const backupCodes = JSON.parse(user.mfa_backup_codes);
        const codeIndex = backupCodes.indexOf(code);

        if (codeIndex === -1) {
            return false;
        }

        // Remove used backup code
        backupCodes.splice(codeIndex, 1);
        await User.update(
            { mfa_backup_codes: JSON.stringify(backupCodes) },
            { where: { user_id: userId } }
        );

        // Log backup code usage
        const auditService = new AuditService();
        await auditService.logAuthEvent(userId, 'MFA_BACKUP_CODE_USED', {
            remainingCodes: backupCodes.length
        });

        // Warn if running low on backup codes
        if (backupCodes.length <= 2) {
            await this.sendLowBackupCodesWarning(userId);
        }

        return true;
    }

    generateBackupCodes(count = 8) {
        const codes = [];
        for (let i = 0; i < count; i++) {
            codes.push(crypto.randomInt(10000000, 99999999).toString());
        }
        return JSON.stringify(codes);
    }

    async regenerateBackupCodes(userId) {
        const newCodes = this.generateBackupCodes();
        
        await User.update(
            { mfa_backup_codes: newCodes },
            { where: { user_id: userId } }
        );

        // Log the event
        const auditService = new AuditService();
        await auditService.logAuthEvent(userId, 'MFA_BACKUP_CODES_REGENERATED');

        return JSON.parse(newCodes);
    }

    async disableMFA(userId, currentPassword, mfaToken) {
        const user = await User.findByPk(userId);
        if (!user) {
            throw new Error('User not found');
        }

        // Verify current password
        const authService = new AuthService();
        const isValidPassword = await authService.verifyPassword(currentPassword, user.password_hash);
        if (!isValidPassword) {
            throw new Error('Invalid password');
        }

        // Verify MFA token
        const isValidMFA = await this.verifyTOTP(userId, mfaToken);
        if (!isValidMFA) {
            throw new Error('Invalid MFA token');
        }

        // Disable MFA
        await User.update(
            {
                mfa_enabled: false,
                mfa_secret: null,
                mfa_backup_codes: null
            },
            { where: { user_id: userId } }
        );

        // Log the event
        const auditService = new AuditService();
        await auditService.logAuthEvent(userId, 'MFA_DISABLED');

        return { success: true };
    }

    async sendSMSCode(userId, phoneNumber) {
        // Generate 6-digit code
        const code = crypto.randomInt(100000, 999999).toString();
        
        // Store code with expiry
        await this.cache.redis.setex(
            `sms_code:${userId}`,
            300, // 5 minutes
            code
        );

        // Send SMS (implementation depends on SMS provider)
        const smsService = require('./SMSService');
        await smsService.send(phoneNumber, `Your verification code is: ${code}`);

        // Log the event
        const auditService = new AuditService();
        await auditService.logAuthEvent(userId, 'SMS_CODE_SENT', {
            phoneNumber: phoneNumber.replace(/(\d{3})\d{3}(\d{4})/, '$1***$2')
        });

        return { success: true };
    }

    async verifySMSCode(userId, code) {
        const storedCode = await this.cache.redis.get(`sms_code:${userId}`);
        
        if (!storedCode || storedCode !== code) {
            return false;
        }

        // Remove used code
        await this.cache.redis.del(`sms_code:${userId}`);

        // Log successful verification
        const auditService = new AuditService();
        await auditService.logAuthEvent(userId, 'SMS_CODE_VERIFIED');

        return true;
    }

    async sendLowBackupCodesWarning(userId) {
        const user = await User.findByPk(userId);
        if (!user) return;

        const backupCodes = JSON.parse(user.mfa_backup_codes || '[]');
        
        const notificationService = require('./NotificationService');
        await notificationService.sendEmail(user.email, 'low-backup-codes', {
            remainingCodes: backupCodes.length,
            regenerateUrl: `${process.env.FRONTEND_URL}/security/mfa`
        });
    }
}

module.exports = MFAService;
```

### Session Management Service

```javascript
// services/SessionService.js
class SessionService {
    constructor() {
        this.cache = new CacheService();
    }

    async createSession(userId, deviceInfo, ipAddress) {
        const deviceService = new DeviceService();
        const device = await deviceService.registerDevice(userId, deviceInfo, ipAddress);

        // Generate session token
        const sessionToken = crypto.randomBytes(32).toString('hex');
        const sessionId = crypto.randomUUID();

        // Calculate expiry
        const expiresAt = new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)); // 7 days

        // Create session record
        const session = await UserSession.create({
            session_id: sessionId,
            user_id: userId,
            session_token: sessionToken,
            device_fingerprint: device.device_fingerprint,
            ip_address: ipAddress,
            user_agent: deviceInfo.userAgent,
            created_at: new Date(),
            expires_at: expiresAt,
            last_activity: new Date(),
            is_active: true
        });

        // Cache session for quick lookup
        await this.cache.redis.setex(
            `session:${sessionToken}`,
            7 * 24 * 60 * 60, // 7 days in seconds
            JSON.stringify({
                sessionId: sessionId,
                userId: userId,
                deviceFingerprint: device.device_fingerprint,
                expiresAt: expiresAt.toISOString()
            })
        );

        return {
            sessionId: sessionId,
            sessionToken: sessionToken,
            expiresAt: expiresAt
        };
    }

    async validateSession(sessionToken) {
        // Check cache first
        const cachedSession = await this.cache.redis.get(`session:${sessionToken}`);
        
        if (cachedSession) {
            const sessionData = JSON.parse(cachedSession);
            
            // Check if expired
            if (new Date(sessionData.expiresAt) > new Date()) {
                return sessionData;
            } else {
                // Remove expired session from cache
                await this.cache.redis.del(`session:${sessionToken}`);
            }
        }

        // Check database
        const session = await UserSession.findOne({
            where: {
                session_token: sessionToken,
                is_active: true,
                expires_at: { [Op.gt]: new Date() }
            }
        });

        if (!session) {
            return null;
        }

        // Update last activity
        await session.update({ last_activity: new Date() });

        // Re-cache the session
        await this.cache.redis.setex(
            `session:${sessionToken}`,
            Math.floor((session.expires_at - Date.now()) / 1000),
            JSON.stringify({
                sessionId: session.session_id,
                userId: session.user_id,
                deviceFingerprint: session.device_fingerprint,
                expiresAt: session.expires_at.toISOString()
            })
        );

        return {
            sessionId: session.session_id,
            userId: session.user_id,
            deviceFingerprint: session.device_fingerprint,
            expiresAt: session.expires_at.toISOString()
        };
    }

    async extendSession(sessionToken, extensionHours = 24) {
        const session = await UserSession.findOne({
            where: {
                session_token: sessionToken,
                is_active: true
            }
        });

        if (!session) {
            throw new Error('Session not found');
        }

        const newExpiryTime = new Date(Date.now() + (extensionHours * 60 * 60 * 1000));
        
        await session.update({
            expires_at: newExpiryTime,
            last_activity: new Date()
        });

        // Update cache
        await this.cache.redis.setex(
            `session:${sessionToken}`,
            extensionHours * 60 * 60,
            JSON.stringify({
                sessionId: session.session_id,
                userId: session.user_id,
                deviceFingerprint: session.device_fingerprint,
                expiresAt: newExpiryTime.toISOString()
            })
        );

        return newExpiryTime;
    }

    async terminateSession(sessionToken) {
        // Remove from cache
        await this.cache.redis.del(`session:${sessionToken}`);

        // Deactivate in database
        await UserSession.update(
            { 
                is_active: false,
                terminated_at: new Date()
            },
            {
                where: { session_token: sessionToken }
            }
        );
    }

    async terminateAllUserSessions(userId, exceptSessionToken = null) {
        const whereClause = {
            user_id: userId,
            is_active: true
        };

        if (exceptSessionToken) {
            whereClause.session_token = { [Op.ne]: exceptSessionToken };
        }

        // Get all sessions to remove from cache
        const sessions = await UserSession.findAll({
            where: whereClause,
            attributes: ['session_token']
        });

        // Remove from cache
        const cachePromises = sessions.map(session => 
            this.cache.redis.del(`session:${session.session_token}`)
        );
        await Promise.all(cachePromises);

        // Deactivate in database
        await UserSession.update(
            { 
                is_active: false,
                terminated_at: new Date()
            },
            { where: whereClause }
        );

        return sessions.length;
    }

    async getUserActiveSessions(userId) {
        const sessions = await UserSession.findAll({
            where: {
                user_id: userId,
                is_active: true,
                expires_at: { [Op.gt]: new Date() }
            },
            order: [['last_activity', 'DESC']]
        });

        return sessions.map(session => ({
            sessionId: session.session_id,
            deviceFingerprint: session.device_fingerprint,
            ipAddress: session.ip_address,
            userAgent: session.user_agent,
            createdAt: session.created_at,
            lastActivity: session.last_activity,
            expiresAt: session.expires_at
        }));
    }

    async cleanupExpiredSessions() {
        // Remove expired sessions from database
        const deleted = await UserSession.destroy({
            where: {
                expires_at: { [Op.lt]: new Date() }
            }
        });

        console.log(`Cleaned up ${deleted} expired sessions`);
        return deleted;
    }

    async detectConcurrentSessions(userId, maxConcurrentSessions = 5) {
        const activeSessions = await this.getUserActiveSessions(userId);
        
        if (activeSessions.length > maxConcurrentSessions) {
            // Terminate oldest sessions
            const sessionsToTerminate = activeSessions
                .slice(maxConcurrentSessions)
                .map(session => session.sessionId);

            await UserSession.update(
                { 
                    is_active: false,
                    terminated_at: new Date(),
                    termination_reason: 'CONCURRENT_SESSION_LIMIT'
                },
                {
                    where: {
                        session_id: { [Op.in]: sessionsToTerminate }
                    }
                }
            );

            // Log the event
            const auditService = new AuditService();
            await auditService.logAuthEvent(userId, 'CONCURRENT_SESSIONS_TERMINATED', {
                terminatedSessions: sessionsToTerminate.length,
                reason: 'Exceeded maximum concurrent sessions limit'
            });

            return sessionsToTerminate.length;
        }

        return 0;
    }
}

module.exports = SessionService;
```

## Advanced Authorization Features

### Dynamic Permission System

```javascript
// services/DynamicPermissionService.js
class DynamicPermissionService {
    constructor() {
        this.cache = new CacheService();
        this.permissionCache = new Map();
    }

    async evaluateContextualPermission(userId, resource, action, context = {}) {
        // Get base permissions
        const hasBasePermission = await this.hasPermission(userId, resource, action);
        if (!hasBasePermission) {
            return { allowed: false, reason: 'NO_BASE_PERMISSION' };
        }

        // Apply contextual rules
        const contextualRules = await this.getContextualRules(resource, action);
        
        for (const rule of contextualRules) {
            const ruleResult = await this.evaluateRule(rule, userId, context);
            if (!ruleResult.allowed) {
                return ruleResult;
            }
        }

        return { allowed: true };
    }

    async evaluateRule(rule,