# Developer Handover Document: Authentication & Authorization Services (Part 7)

*Continuation from Part 6...*

```javascript
// Logout Route
router.post('/logout',
    authMiddleware.authenticate,
    async (req, res) => {
        try {
            const userId = req.user.id;
            const refreshToken = req.cookies.refreshToken || req.body.refreshToken;

            await authService.logout(userId, refreshToken);

            // Clear cookies
            res.clearCookie('refreshToken');
            res.clearCookie('sessionId');

            res.json({
                success: true,
                message: 'Logged out successfully'
            });
        } catch (error) {
            console.error('Logout error:', error);
            res.status(500).json({
                success: false,
                message: 'Logout failed'
            });
        }
    }
);

// Refresh Token Route
router.post('/refresh-token',
    [
        body('refreshToken').optional().isString(),
    ],
    async (req, res) => {
        try {
            const refreshToken = req.cookies.refreshToken || req.body.refreshToken;

            if (!refreshToken) {
                return res.status(401).json({
                    success: false,
                    message: 'Refresh token required'
                });
            }

            const result = await authService.refreshToken(refreshToken);

            if (!result.success) {
                res.clearCookie('refreshToken');
                return res.status(401).json(result);
            }

            res.json(result);
        } catch (error) {
            console.error('Token refresh error:', error);
            res.status(500).json({
                success: false,
                message: 'Token refresh failed'
            });
        }
    }
);

// Password Reset Request
router.post('/forgot-password',
    [
        body('email').isEmail().withMessage('Valid email required')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { email } = req.body;
            const ipAddress = authMiddleware.getClientIP(req);

            const result = await authService.requestPasswordReset(email, ipAddress);

            // Always return success to prevent email enumeration
            res.json({
                success: true,
                message: 'If the email exists, a password reset link has been sent'
            });
        } catch (error) {
            console.error('Password reset request error:', error);
            res.status(500).json({
                success: false,
                message: 'Password reset request failed'
            });
        }
    }
);

// Password Reset Confirmation
router.post('/reset-password',
    [
        body('token').notEmpty().withMessage('Reset token required'),
        body('newPassword').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
        body('confirmPassword').custom((value, { req }) => {
            if (value !== req.body.newPassword) {
                throw new Error('Passwords do not match');
            }
            return true;
        })
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { token, newPassword } = req.body;
            const ipAddress = authMiddleware.getClientIP(req);

            const result = await authService.resetPassword(token, newPassword, ipAddress);

            res.json(result);
        } catch (error) {
            console.error('Password reset error:', error);
            res.status(500).json({
                success: false,
                message: 'Password reset failed'
            });
        }
    }
);

// Change Password (Authenticated)
router.post('/change-password',
    authMiddleware.authenticate,
    [
        body('currentPassword').notEmpty().withMessage('Current password required'),
        body('newPassword').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
        body('confirmPassword').custom((value, { req }) => {
            if (value !== req.body.newPassword) {
                throw new Error('Passwords do not match');
            }
            return true;
        })
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const userId = req.user.id;
            const { currentPassword, newPassword } = req.body;
            const ipAddress = authMiddleware.getClientIP(req);

            const result = await authService.changePassword(userId, currentPassword, newPassword, ipAddress);

            res.json(result);
        } catch (error) {
            console.error('Password change error:', error);
            res.status(500).json({
                success: false,
                message: 'Password change failed'
            });
        }
    }
);

// Setup MFA
router.post('/setup-mfa',
    authMiddleware.authenticate,
    async (req, res) => {
        try {
            const userId = req.user.id;
            
            const setupResult = await mfaService.setupMFA(userId);

            res.json(setupResult);
        } catch (error) {
            console.error('MFA setup error:', error);
            res.status(500).json({
                success: false,
                message: 'MFA setup failed'
            });
        }
    }
);

// Confirm MFA Setup
router.post('/confirm-mfa-setup',
    authMiddleware.authenticate,
    [
        body('token').isLength({ min: 6, max: 6 }).withMessage('Invalid MFA token'),
        body('secret').notEmpty().withMessage('Secret required')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const userId = req.user.id;
            const { token, secret } = req.body;

            const result = await mfaService.confirmMFASetup(userId, secret, token);

            res.json(result);
        } catch (error) {
            console.error('MFA confirmation error:', error);
            res.status(500).json({
                success: false,
                message: 'MFA confirmation failed'
            });
        }
    }
);

// Disable MFA
router.post('/disable-mfa',
    authMiddleware.authenticate,
    [
        body('password').notEmpty().withMessage('Password required'),
        body('token').isLength({ min: 6, max: 6 }).withMessage('Invalid MFA token')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const userId = req.user.id;
            const { password, token } = req.body;

            const result = await mfaService.disableMFA(userId, password, token);

            res.json(result);
        } catch (error) {
            console.error('MFA disable error:', error);
            res.status(500).json({
                success: false,
                message: 'MFA disable failed'
            });
        }
    }
);

// Generate Backup Codes
router.post('/generate-backup-codes',
    authMiddleware.authenticate,
    [
        body('password').notEmpty().withMessage('Password required')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const userId = req.user.id;
            const { password } = req.body;

            const result = await mfaService.generateBackupCodes(userId, password);

            res.json(result);
        } catch (error) {
            console.error('Backup codes generation error:', error);
            res.status(500).json({
                success: false,
                message: 'Backup codes generation failed'
            });
        }
    }
);

// Get User Profile
router.get('/profile',
    authMiddleware.authenticate,
    async (req, res) => {
        try {
            const userId = req.user.id;
            
            const user = await User.findByPk(userId, {
                attributes: { exclude: ['password_hash', 'mfa_secret'] },
                include: [
                    { model: UserRole, include: [Role] },
                    { model: Department },
                    { model: UserSession, where: { is_active: true }, required: false }
                ]
            });

            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            res.json({
                success: true,
                data: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    firstName: user.first_name,
                    lastName: user.last_name,
                    department: user.Department,
                    roles: user.UserRoles.map(ur => ({
                        id: ur.Role.id,
                        name: ur.Role.role_name,
                        description: ur.Role.description
                    })),
                    mfaEnabled: user.mfa_enabled,
                    lastLoginAt: user.last_login_at,
                    activeSessions: user.UserSessions ? user.UserSessions.length : 0,
                    accountStatus: user.account_status,
                    createdAt: user.created_at
                }
            });
        } catch (error) {
            console.error('Profile fetch error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to fetch profile'
            });
        }
    }
);

// Update Profile
router.put('/profile',
    authMiddleware.authenticate,
    [
        body('firstName').optional().trim().isLength({ min: 1 }).withMessage('First name cannot be empty'),
        body('lastName').optional().trim().isLength({ min: 1 }).withMessage('Last name cannot be empty'),
        body('email').optional().isEmail().withMessage('Valid email required')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const userId = req.user.id;
            const updateData = {};

            if (req.body.firstName) updateData.first_name = req.body.firstName;
            if (req.body.lastName) updateData.last_name = req.body.lastName;
            if (req.body.email) updateData.email = req.body.email;

            await User.update(updateData, { where: { id: userId } });

            res.json({
                success: true,
                message: 'Profile updated successfully'
            });
        } catch (error) {
            console.error('Profile update error:', error);
            res.status(500).json({
                success: false,
                message: 'Profile update failed'
            });
        }
    }
);

// Get Active Sessions
router.get('/sessions',
    authMiddleware.authenticate,
    async (req, res) => {
        try {
            const userId = req.user.id;
            
            const sessions = await UserSession.findAll({
                where: { 
                    user_id: userId,
                    is_active: true
                },
                order: [['last_activity', 'DESC']],
                attributes: { exclude: ['session_token'] }
            });

            res.json({
                success: true,
                data: sessions.map(session => ({
                    id: session.id,
                    deviceInfo: session.device_info,
                    ipAddress: session.ip_address,
                    lastActivity: session.last_activity,
                    createdAt: session.created_at,
                    isCurrent: session.id === req.sessionId
                }))
            });
        } catch (error) {
            console.error('Sessions fetch error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to fetch sessions'
            });
        }
    }
);

// Terminate Session
router.delete('/sessions/:sessionId',
    authMiddleware.authenticate,
    async (req, res) => {
        try {
            const userId = req.user.id;
            const { sessionId } = req.params;

            const session = await UserSession.findOne({
                where: { 
                    id: sessionId,
                    user_id: userId,
                    is_active: true
                }
            });

            if (!session) {
                return res.status(404).json({
                    success: false,
                    message: 'Session not found'
                });
            }

            await authService.terminateSession(sessionId);

            res.json({
                success: true,
                message: 'Session terminated successfully'
            });
        } catch (error) {
            console.error('Session termination error:', error);
            res.status(500).json({
                success: false,
                message: 'Session termination failed'
            });
        }
    }
);

// Terminate All Sessions (except current)
router.delete('/sessions',
    authMiddleware.authenticate,
    async (req, res) => {
        try {
            const userId = req.user.id;
            const currentSessionId = req.sessionId;

            await authService.terminateAllUserSessions(userId, currentSessionId);

            res.json({
                success: true,
                message: 'All other sessions terminated successfully'
            });
        } catch (error) {
            console.error('Sessions termination error:', error);
            res.status(500).json({
                success: false,
                message: 'Sessions termination failed'
            });
        }
    }
);

module.exports = router;
```

### Permission Management Routes

```javascript
// routes/permissions.js
const express = require('express');
const router = express.Router();
const PermissionService = require('../services/PermissionService');
const authMiddleware = require('../middleware/authMiddleware');
const { body, param, query, validationResult } = require('express-validator');

const permissionService = new PermissionService();

// Check user permissions
router.get('/check',
    authMiddleware.authenticate,
    [
        query('resource').notEmpty().withMessage('Resource is required'),
        query('action').notEmpty().withMessage('Action is required')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const userId = req.user.id;
            const { resource, action } = req.query;

            const hasPermission = await permissionService.hasPermission(userId, resource, action);

            res.json({
                success: true,
                data: {
                    hasPermission,
                    resource,
                    action
                }
            });
        } catch (error) {
            console.error('Permission check error:', error);
            res.status(500).json({
                success: false,
                message: 'Permission check failed'
            });
        }
    }
);

// Get user permissions
router.get('/user/:userId',
    authMiddleware.authenticate,
    authMiddleware.requirePermission('USER_MANAGEMENT', 'READ'),
    [
        param('userId').isInt().withMessage('Valid user ID required')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { userId } = req.params;
            const permissions = await permissionService.getUserPermissions(userId);

            res.json({
                success: true,
                data: permissions
            });
        } catch (error) {
            console.error('User permissions fetch error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to fetch user permissions'
            });
        }
    }
);

// Grant dynamic permission
router.post('/grant',
    authMiddleware.authenticate,
    authMiddleware.requirePermission('PERMISSION_MANAGEMENT', 'CREATE'),
    [
        body('userId').isInt().withMessage('Valid user ID required'),
        body('resource').notEmpty().withMessage('Resource is required'),
        body('action').notEmpty().withMessage('Action is required'),
        body('expiresAt').optional().isISO8601().withMessage('Valid expiration date required'),
        body('context').optional().isObject().withMessage('Context must be an object')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const grantedBy = req.user.id;
            const { userId, resource, action, expiresAt, context } = req.body;

            const result = await permissionService.grantDynamicPermission({
                userId,
                resource,
                action,
                grantedBy,
                expiresAt: expiresAt ? new Date(expiresAt) : null,
                context
            });

            res.json(result);
        } catch (error) {
            console.error('Permission grant error:', error);
            res.status(500).json({
                success: false,
                message: 'Permission grant failed'
            });
        }
    }
);

// Revoke dynamic permission
router.delete('/revoke/:permissionId',
    authMiddleware.authenticate,
    authMiddleware.requirePermission('PERMISSION_MANAGEMENT', 'DELETE'),
    [
        param('permissionId').isInt().withMessage('Valid permission ID required')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { permissionId } = req.params;
            const revokedBy = req.user.id;

            const result = await permissionService.revokeDynamicPermission(permissionId, revokedBy);

            res.json(result);
        } catch (error) {
            console.error('Permission revoke error:', error);
            res.status(500).json({
                success: false,
                message: 'Permission revoke failed'
            });
        }
    }
);

// Get role permissions
router.get('/role/:roleId',
    authMiddleware.authenticate,
    authMiddleware.requirePermission('ROLE_MANAGEMENT', 'READ'),
    [
        param('roleId').isInt().withMessage('Valid role ID required')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { roleId } = req.params;
            const permissions = await permissionService.getRolePermissions(roleId);

            res.json({
                success: true,
                data: permissions
            });
        } catch (error) {
            console.error('Role permissions fetch error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to fetch role permissions'
            });
        }
    }
);

module.exports = router;
```

### Security Audit Routes

```javascript
// routes/security.js
const express = require('express');
const router = express.Router();
const SecurityAuditService = require('../services/SecurityAuditService');
const authMiddleware = require('../middleware/authMiddleware');
const { param, query, validationResult } = require('express-validator');

const securityAuditService = new SecurityAuditService();

// Perform security audit
router.post('/audit/:userId',
    authMiddleware.authenticate,
    authMiddleware.requirePermission('SECURITY_AUDIT', 'CREATE'),
    [
        param('userId').isInt().withMessage('Valid user ID required'),
        query('type').optional().isIn(['USER_PERMISSIONS', 'ACCESS_PATTERNS', 'SECURITY_VIOLATIONS', 'COMPREHENSIVE'])
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { userId } = req.params;
            const auditType = req.query.type || 'COMPREHENSIVE';

            const auditResults = await securityAuditService.performSecurityAudit(userId, auditType);

            res.json({
                success: true,
                data: auditResults
            });
        } catch (error) {
            console.error('Security audit error:', error);
            res.status(500).json({
                success: false,
                message: 'Security audit failed'
            });
        }
    }
);

// Get audit history
router.get('/audit-history/:userId',
    authMiddleware.authenticate,
    authMiddleware.requirePermission('SECURITY_AUDIT', 'READ'),
    [
        param('userId').isInt().withMessage('Valid user ID required'),
        query('limit').optional().isInt({ min: 1, max: 100 }),
        query('offset').optional().isInt({ min: 0 })
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { userId } = req.params;
            const limit = parseInt(req.query.limit) || 20;
            const offset = parseInt(req.query.offset) || 0;

            const auditHistory = await SecurityAudit.findAndCountAll({
                where: { user_id: userId },
                order: [['created_at', 'DESC']],
                limit,
                offset,
                attributes: { exclude: ['findings', 'recommendations'] }
            });

            res.json({
                success: true,
                data: {
                    audits: auditHistory.rows,
                    total: auditHistory.count,
                    limit,
                    offset
                }
            });
        } catch (error) {
            console.error('Audit history fetch error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to fetch audit history'
            });
        }
    }
);

// Generate compliance report
router.post('/compliance-report',
    authMiddleware.authenticate,
    authMiddleware.requirePermission('COMPLIANCE_REPORTING', 'CREATE'),
    [
        query('startDate').isISO8601().withMessage('Valid start date required'),
        query('endDate').isISO8601().withMessage('Valid end date required')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { startDate, endDate } = req.query;

            const report = await securityAuditService.generateComplianceReport(
                new Date(startDate),
                new Date(endDate)
            );

            res.json({
                success: true,
                data: report
            });
        } catch (error) {
            console.error('Compliance report error:', error);
            res.status(500).json({
                success: false,
                message: 'Compliance report generation failed'
            });
        }
    }
);

module.exports = router;
```

### Authentication Middleware Extensions

```javascript
// middleware/authMiddleware.js (Additional functions)

// Device Trust Middleware
exports.checkDeviceTrust = async (req, res, next) => {
    try {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }

        const deviceFingerprint = req.body.deviceFingerprint || req.headers['x-device-fingerprint'];
        
        if (!deviceFingerprint) {
            return res.status(400).json({
                success: false,
                message: 'Device fingerprint required'
            });
        }

        const trustedDeviceService = new (require('../services/TrustedDeviceService'))();
        const deviceTrust = await trustedDeviceService.checkDeviceTrust(req.user.id, deviceFingerprint);

        if (!deviceTrust.trusted && deviceTrust.riskLevel === 'HIGH') {
            // Log security event
            await this.logSecurityEvent(req.user.id, 'UNTRUSTED_DEVICE_ACCESS', {
                deviceFingerprint,
                ipAddress: this.getClientIP(req),
                riskLevel: deviceTrust.riskLevel
            });

            return res.status(403).json({
                success: false,
                message: 'Device not trusted',
                requiresVerification: true
            });
        }

        req.deviceTrust = deviceTrust;
        next();
    } catch (error) {
        console.error('Device trust check error:', error);
        res.status(500).json({
            success: false,
            message: 'Device trust check failed'
        });
    }
};

// IP Restriction Middleware
exports.checkIPRestrictions = async (req, res, next) => {
    try {
        const ipAddress = this.getClientIP(req);
        const authConfig = require('../config/authConfig');

        // Check IP whitelist
        if (authConfig.ipSecurity.whitelist.length > 0) {
            if (!authConfig.ipSecurity.whitelist.includes(ipAddress)) {
                await this.logSecurityEvent(null, 'IP_NOT_WHITELISTED', {
                    ipAddress,
                    userAgent: req.get('User-Agent')
                });

                return res.status(403).json({
                    success: false,
                    message: 'Access denied from this IP address'
                });
            }
        }

        // Check IP blacklist
        if (authConfig.ipSecurity.blacklist.includes(ipAddress)) {
            await this.logSecurityEvent(null, 'IP_BLACKLISTED', {
                ipAddress,
                userAgent: req.get('User-Agent')
            });

            return res.status(403).json({
                success: false,
                message: 'Access denied from this IP address'
            });
        }

        // Check for auto-blocked IPs
        const blockedIP = await BlockedIP.findOne({
            where: {
                ip_address: ipAddress,
                blocked_until: { [Op.gt]: new Date() }
            }
        });

        if (blockedIP) {
            return res.status(403).json({
                success: false,
                message: 'IP address is temporarily blocked',
                blockedUntil: blockedIP.blocked_until
            });
        }

        next();
    } catch (error) {
        console.error('IP restriction check error:', error);
        res.status(500).json({
            success: false,
            message: 'IP restriction check failed'
        });
    }
};

// Session Validation Middleware
exports.validateSession = async (req, res, next) => {
    try {
        if (!req.user) {
            return next();
        }

        const sessionToken = req.headers.authorization?.split(' ')[1];
        
        if (!sessionToken) {
            return res.status(401).json({
                success: false,
                message: 'Session token required'
            });
        }

        const session = await UserSession.findOne({
            where: {
                session_token: sessionToken,
                user_id: req.user.id,
                is_active: true,
                expires_at: { [Op.gt]: new Date() }
            }
        });

        if (!session) {
            return res.status(401).json({
                success: false,
                message: 'Invalid or expired session'
            });
        }

        // Update last activity
        await session.update({ last_activity: new Date() });

        req.sessionId = session.id;
        next();
    } catch (error) {
        console.error('Session validation error:', error);
        res.status(500).json({
            success: false,
            message: 'Session validation failed'
        });
    }
};

// Audit Logging Middleware
exports.auditLog = (eventType) => {
    return async (req, res, next) => {
        const originalSend = res.send;
        const startTime = Date.now();

        res.send = function(data) {
            const endTime = Date.now();
            const responseTime = endTime - startTime;

            // Log the audit event
            setImmediate(async () => {
                try {
                    await AuditLog.create({
                        user_id: req.user?.id || null,
                        event_type: eventType,
                        resource: req.route?.path || req.path,
                        action: req.method,
                        ip_address: exports.getClientIP(req),
                        user_agent: req.get('User-Agent'),
                        request_data: req.method !== 'GET' ? JSON.stringify(req.body) : null,
                        response_status: res.statusCode,
                        response_time: responseTime,
                        details: JSON.stringify({
                            url: req.originalUrl,
                            params: req.params,
                            query: req.query
                        })
                    });
                } catch (error) {
                    console.error('Audit logging error:', error);
                }
            });

            originalSend.call(this, data);
        };

        next();
    };
};

// Security Headers Middleware
exports.securityHeaders = (req, res, next) => {
    const authConfig = require('../config/authConfig');

    if