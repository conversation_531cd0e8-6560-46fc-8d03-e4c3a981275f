# Developer Handover Document: Authentication & Authorization Services (Part 2)

*Continuation from Part 1...*

```javascript
// middleware/permissionMiddleware.js (continued)
const requirePermission = (resource, action) => {
    return async (req, res, next) => {
        try {
            const { userId } = req.user;
            
            const authService = new AuthorizationService();
            const hasPermission = await authService.hasPermission(userId, resource, action);

            if (!hasPermission) {
                await authService.logAuditEvent(userId, 'ACCESS_DENIED', resource, req.params.id || null, {
                    attempted_action: action,
                    endpoint: req.originalUrl,
                    method: req.method
                });

                return res.status(403).json({
                    error: 'Insufficient permissions',
                    code: 'PERMISSION_DENIED',
                    required: `${resource}:${action}`
                });
            }

            next();
        } catch (error) {
            console.error('Permission check error:', error);
            return res.status(500).json({
                error: 'Permission validation failed',
                code: 'PERMISSION_ERROR'
            });
        }
    };
};

const requireAnyPermission = (permissions) => {
    return async (req, res, next) => {
        try {
            const { userId } = req.user;
            
            const authService = new AuthorizationService();
            const hasAnyPermission = await authService.hasAnyPermission(userId, permissions);

            if (!hasAnyPermission) {
                return res.status(403).json({
                    error: 'Insufficient permissions',
                    code: 'PERMISSION_DENIED',
                    required_any: permissions
                });
            }

            next();
        } catch (error) {
            console.error('Permission check error:', error);
            return res.status(500).json({
                error: 'Permission validation failed',
                code: 'PERMISSION_ERROR'
            });
        }
    };
};

const requireRole = (roleName) => {
    return async (req, res, next) => {
        try {
            const { userId } = req.user;
            
            const authService = new AuthorizationService();
            const hasRole = await authService.hasRole(userId, roleName);

            if (!hasRole) {
                return res.status(403).json({
                    error: 'Insufficient role',
                    code: 'ROLE_REQUIRED',
                    required_role: roleName
                });
            }

            next();
        } catch (error) {
            console.error('Role check error:', error);
            return res.status(500).json({
                error: 'Role validation failed',
                code: 'ROLE_ERROR'
            });
        }
    };
};

module.exports = {
    requirePermission,
    requireAnyPermission,
    requireRole
};
```

### 3. Data Filtering Service

#### Resource-Level Security Implementation
```javascript
// services/DataFilterService.js
class DataFilterService {
    async filterInventoryData(userId, queryData, operation = 'read') {
        const authService = new AuthorizationService();
        
        // Get user's location and department restrictions
        const userRestrictions = await this.getUserDataRestrictions(userId);
        
        let filteredQuery = { ...queryData };

        // Apply location-based filtering
        if (userRestrictions.locations && userRestrictions.locations.length > 0) {
            filteredQuery.where = {
                ...filteredQuery.where,
                location_id: { [Op.in]: userRestrictions.locations }
            };
        }

        // Apply department-based filtering
        if (userRestrictions.departments && userRestrictions.departments.length > 0) {
            filteredQuery.where = {
                ...filteredQuery.where,
                department_id: { [Op.in]: userRestrictions.departments }
            };
        }

        // Apply item category restrictions
        if (userRestrictions.categories && userRestrictions.categories.length > 0) {
            filteredQuery.where = {
                ...filteredQuery.where,
                category_id: { [Op.in]: userRestrictions.categories }
            };
        }

        // Check for sensitive data access
        const canViewSensitive = await authService.hasPermission(userId, 'inventory', 'view_sensitive');
        if (!canViewSensitive) {
            filteredQuery.attributes = {
                exclude: ['cost_price', 'supplier_info', 'profit_margin']
            };
        }

        return filteredQuery;
    }

    async getUserDataRestrictions(userId) {
        // Check if user has global access
        const authService = new AuthorizationService();
        const hasGlobalAccess = await authService.hasPermission(userId, 'inventory', 'global_access');
        
        if (hasGlobalAccess) {
            return { unrestricted: true };
        }

        // Get user's specific restrictions from database
        const restrictions = await UserDataRestriction.findAll({
            where: { user_id: userId, is_active: true }
        });

        const result = {
            locations: [],
            departments: [],
            categories: []
        };

        restrictions.forEach(restriction => {
            switch (restriction.restriction_type) {
                case 'location':
                    result.locations.push(restriction.resource_id);
                    break;
                case 'department':
                    result.departments.push(restriction.resource_id);
                    break;
                case 'category':
                    result.categories.push(restriction.resource_id);
                    break;
            }
        });

        return result;
    }

    async validateItemAccess(userId, itemId, operation = 'read') {
        const authService = new AuthorizationService();
        
        // Check if user has global item access
        const hasGlobalAccess = await authService.hasPermission(userId, 'inventory', 'global_access');
        if (hasGlobalAccess) return true;

        // Get item details
        const item = await InventoryItem.findByPk(itemId);
        if (!item) throw new Error('Item not found');

        // Check location access
        if (item.location_id) {
            const hasLocationAccess = await this.hasLocationAccess(userId, item.location_id);
            if (!hasLocationAccess) return false;
        }

        // Check department access
        if (item.department_id) {
            const hasDepartmentAccess = await this.hasDepartmentAccess(userId, item.department_id);
            if (!hasDepartmentAccess) return false;
        }

        // Check category access
        if (item.category_id) {
            const hasCategoryAccess = await this.hasCategoryAccess(userId, item.category_id);
            if (!hasCategoryAccess) return false;
        }

        return true;
    }

    async hasLocationAccess(userId, locationId) {
        const restrictions = await this.getUserDataRestrictions(userId);
        if (restrictions.unrestricted) return true;
        
        return restrictions.locations.length === 0 || 
               restrictions.locations.includes(locationId);
    }

    async hasDepartmentAccess(userId, departmentId) {
        const restrictions = await this.getUserDataRestrictions(userId);
        if (restrictions.unrestricted) return true;
        
        return restrictions.departments.length === 0 || 
               restrictions.departments.includes(departmentId);
    }

    async hasCategoryAccess(userId, categoryId) {
        const restrictions = await this.getUserDataRestrictions(userId);
        if (restrictions.unrestricted) return true;
        
        return restrictions.categories.length === 0 || 
               restrictions.categories.includes(categoryId);
    }
}
```

## API Endpoints

### Authentication Endpoints

```javascript
// routes/auth.js
const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const router = express.Router();

// Rate limiting
const loginLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per window
    message: { error: 'Too many login attempts, please try again later' },
    standardHeaders: true,
    legacyHeaders: false
});

const resetLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // 3 reset requests per hour
    message: { error: 'Too many password reset attempts, please try again later' }
});

// Validation rules
const loginValidation = [
    body('email').isEmail().normalizeEmail(),
    body('password').isLength({ min: 1 }).trim(),
    body('mfaToken').optional().isLength({ min: 6, max: 6 }).isNumeric()
];

const registerValidation = [
    body('email').isEmail().normalizeEmail(),
    body('username').isLength({ min: 3, max: 50 }).matches(/^[a-zA-Z0-9_]+$/),
    body('password').isLength({ min: 8 }),
    body('firstName').isLength({ min: 1, max: 50 }).trim(),
    body('lastName').isLength({ min: 1, max: 50 }).trim(),
    body('phone').optional().isMobilePhone()
];

// POST /auth/login
router.post('/login', loginLimiter, loginValidation, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
        }

        const { email, password, mfaToken } = req.body;
        const authService = new AuthService();
        
        const result = await authService.validateLogin(email, password, mfaToken);

        if (result.requiresMFA) {
            return res.status(200).json({
                message: 'MFA required',
                tempToken: result.tempToken,
                requiresMFA: true
            });
        }

        res.status(200).json({
            message: 'Login successful',
            user: result.user,
            tokens: result.tokens
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(401).json({
            error: 'Authentication failed',
            message: error.message
        });
    }
});

// POST /auth/refresh
router.post('/refresh', async (req, res) => {
    try {
        const { refreshToken } = req.body;
        
        if (!refreshToken) {
            return res.status(400).json({ error: 'Refresh token required' });
        }

        const tokenService = new TokenService();
        const tokens = await tokenService.refreshAccessToken(refreshToken);

        res.status(200).json(tokens);

    } catch (error) {
        console.error('Token refresh error:', error);
        res.status(401).json({
            error: 'Token refresh failed',
            message: error.message
        });
    }
});

// POST /auth/logout
router.post('/logout', authenticateToken, async (req, res) => {
    try {
        const { userId, sessionId } = req.user;
        
        const tokenService = new TokenService();
        await tokenService.revokeToken(sessionId, userId);

        res.status(200).json({ message: 'Logout successful' });

    } catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({
            error: 'Logout failed',
            message: error.message
        });
    }
});

// POST /auth/forgot-password
router.post('/forgot-password', resetLimiter, async (req, res) => {
    try {
        const { email } = req.body;
        
        if (!email) {
            return res.status(400).json({ error: 'Email is required' });
        }

        const passwordService = new PasswordService();
        const result = await passwordService.initiatePasswordReset(email);

        res.status(200).json(result);

    } catch (error) {
        console.error('Password reset initiation error:', error);
        res.status(500).json({
            error: 'Password reset failed',
            message: error.message
        });
    }
});

// POST /auth/reset-password
router.post('/reset-password', async (req, res) => {
    try {
        const { token, newPassword } = req.body;
        
        if (!token || !newPassword) {
            return res.status(400).json({ error: 'Token and new password are required' });
        }

        const passwordService = new PasswordService();
        const result = await passwordService.resetPassword(token, newPassword);

        res.status(200).json(result);

    } catch (error) {
        console.error('Password reset error:', error);
        res.status(400).json({
            error: 'Password reset failed',
            message: error.message
        });
    }
});

// POST /auth/change-password
router.post('/change-password', authenticateToken, async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;
        const { userId } = req.user;
        
        if (!currentPassword || !newPassword) {
            return res.status(400).json({ 
                error: 'Current password and new password are required' 
            });
        }

        const passwordService = new PasswordService();
        const result = await passwordService.changePassword(userId, currentPassword, newPassword);

        res.status(200).json(result);

    } catch (error) {
        console.error('Password change error:', error);
        res.status(400).json({
            error: 'Password change failed',
            message: error.message
        });
    }
});

module.exports = router;
```

### MFA Endpoints

```javascript
// routes/mfa.js
const express = require('express');
const router = express.Router();

// POST /mfa/setup
router.post('/setup', authenticateToken, async (req, res) => {
    try {
        const { userId } = req.user;
        
        const mfaService = new MFAService();
        const result = await mfaService.setupTOTP(userId);

        res.status(200).json({
            message: 'MFA setup initiated',
            qrCode: result.qrCode,
            manualEntryKey: result.manualEntryKey
        });

    } catch (error) {
        console.error('MFA setup error:', error);
        res.status(500).json({
            error: 'MFA setup failed',
            message: error.message
        });
    }
});

// POST /mfa/verify
router.post('/verify', authenticateToken, async (req, res) => {
    try {
        const { token } = req.body;
        const { userId } = req.user;
        
        if (!token) {
            return res.status(400).json({ error: 'MFA token is required' });
        }

        const mfaService = new MFAService();
        const result = await mfaService.verifyAndEnableTOTP(userId, token);

        res.status(200).json(result);

    } catch (error) {
        console.error('MFA verification error:', error);
        res.status(400).json({
            error: 'MFA verification failed',
            message: error.message
        });
    }
});

// POST /mfa/disable
router.post('/disable', authenticateToken, async (req, res) => {
    try {
        const { currentPassword } = req.body;
        const { userId } = req.user;
        
        if (!currentPassword) {
            return res.status(400).json({ error: 'Current password is required' });
        }

        const mfaService = new MFAService();
        const result = await mfaService.disableMFA(userId, currentPassword);

        res.status(200).json(result);

    } catch (error) {
        console.error('MFA disable error:', error);
        res.status(400).json({
            error: 'MFA disable failed',
            message: error.message
        });
    }
});

module.exports = router;
```

### User Management Endpoints

```javascript
// routes/users.js
const express = require('express');
const router = express.Router();

// GET /users - List all users (Admin only)
router.get('/', 
    authenticateToken, 
    requirePermission('users', 'read'),
    async (req, res) => {
        try {
            const { page = 1, limit = 10, search, status } = req.query;
            const offset = (page - 1) * limit;

            let whereClause = {};
            
            if (search) {
                whereClause[Op.or] = [
                    { first_name: { [Op.iLike]: `%${search}%` } },
                    { last_name: { [Op.iLike]: `%${search}%` } },
                    { email: { [Op.iLike]: `%${search}%` } },
                    { username: { [Op.iLike]: `%${search}%` } }
                ];
            }

            if (status) {
                whereClause.is_active = status === 'active';
            }

            const users = await User.findAndCountAll({
                where: whereClause,
                attributes: { exclude: ['password_hash', 'mfa_secret'] },
                include: [{
                    model: UserRoleAssignment,
                    include: [UserRole]
                }],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['created_at', 'DESC']]
            });

            res.status(200).json({
                users: users.rows,
                pagination: {
                    current_page: parseInt(page),
                    total_pages: Math.ceil(users.count / limit),
                    total_users: users.count,
                    per_page: parseInt(limit)
                }
            });

        } catch (error) {
            console.error('Users list error:', error);
            res.status(500).json({
                error: 'Failed to retrieve users',
                message: error.message
            });
        }
    }
);

// GET /users/:id - Get specific user
router.get('/:id', 
    authenticateToken, 
    requireAnyPermission(['users:read', 'users:read_own']),
    async (req, res) => {
        try {
            const { id } = req.params;
            const { userId } = req.user;

            // Check if user can only read their own profile
            const authService = new AuthorizationService();
            const canReadAll = await authService.hasPermission(userId, 'users', 'read');
            
            if (!canReadAll && parseInt(id) !== userId) {
                return res.status(403).json({
                    error: 'Can only access your own profile'
                });
            }

            const user = await User.findByPk(id, {
                attributes: { exclude: ['password_hash', 'mfa_secret'] },
                include: [{
                    model: UserRoleAssignment,
                    include: [UserRole]
                }]
            });

            if (!user) {
                return res.status(404).json({ error: 'User not found' });
            }

            res.status(200).json({ user });

        } catch (error) {
            console.error('User get error:', error);
            res.status(500).json({
                error: 'Failed to retrieve user',
                message: error.message
            });
        }
    }
);

// POST /users/:id/roles - Assign role to user
router.post('/:id/roles', 
    authenticateToken, 
    requirePermission('users', 'manage_roles'),
    async (req, res) => {
        try {
            const { id } = req.params;
            const { roleId } = req.body;
            const { userId } = req.user;

            if (!roleId) {
                return res.status(400).json({ error: 'Role ID is required' });
            }

            const authService = new AuthorizationService();
            const result = await authService.assignRole(parseInt(id), roleId, userId);

            res.status(200).json(result);

        } catch (error) {
            console.error('Role assignment error:', error);
            res.status(400).json({
                error: 'Role assignment failed',
                message: error.message
            });
        }
    }
);

// DELETE /users/:id/roles/:roleId - Revoke role from user
router.delete('/:id/roles/:roleId', 
    authenticateToken, 
    requirePermission('users', 'manage_roles'),
    async (req, res) => {
        try {
            const { id, roleId } = req.params;
            const { userId } = req.user;

            const authService = new AuthorizationService();
            const result = await authService.revokeRole(parseInt(id), parseInt(roleId), userId);

            res.status(200).json(result);

        } catch (error) {
            console.error('Role revocation error:', error);
            res.status(400).json({
                error: 'Role revocation failed',
                message: error.message
            });
        }
    }
);

module.exports = router;
```

## Security Configuration

### Environment Variables

```bash
# .env
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-minimum-256-bits
JWT_REFRESH_SECRET=your-refresh-token-secret-key
JWT_ISSUER=inventory-management-system
JWT_AUDIENCE=inventory-users
ACCESS_TOKEN_EXPIRY=15m
REFRESH_TOKEN_EXPIRY=7d

# Password Security
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SPECIAL=true

# Account Lockout
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=30

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/inventory_db

# Redis
REDIS_URL=redis://localhost:6379

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Application
APP_NAME=Inventory Management System
APP_URL=https://yourdomain.com
FRONTEND_URL=https://app.yourdomain.com

# Security Headers
CORS_ORIGIN=https://app.yourdomain.com
TRUSTED_PROXIES=127.0.0.1,10.0.0.0/8
```

### Security Headers Middleware

```javascript
// middleware/security.js
const helmet = require('helmet');
const cors = require('cors');

const securityMiddleware = (app) => {
    // Helmet for security headers
    app.use(helmet({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'"],
                scriptSrc: ["'self'"],
                imgSrc: ["'self'", "data:", "https:"],
            },
        },
        hsts: {
            maxAge: 31536000,
            includeSubDomains: true,
            preload: true
        }
    }));

    // CORS configuration
    app.use(cors({
        origin: process.env.CORS_ORIGIN?.split(',') || 'http://localhost:3000',
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
        allowedHeaders: ['Content-Type', 'Authorization']
    }));

    // Additional security headers
    app.use((req, res, next) => {
        res.setHeader('X-Content-Type-Options', 'nosniff');
        res.setHeader('X-Frame-Options', 'DENY');
        res.setHeader('X-XSS-Protection', '1; mode=block');
        res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
        next();
    });
};

module.exports = securityMiddleware;
```

## Error Handling

### Global Error Handler

```javascript
// middleware/errorHandler.js
const errorHandler = (err, req, res, next) => {
    console.error('Error:', {
        message: err.message,
        stack: err.stack,
        url: req.originalUrl,
        method: req.method,
        userId: req.user?.userId,
        timestamp: new Date().toISOString()
    });

    // Default error response
    let error = {
        message: 'Internal server error',
        code: 'INTERNAL_ERROR',
        timestamp: new Date().toISOString()
    };

    // Handle specific error types
    if (err.name === 'ValidationError') {
        error = {
            message: 'Validation failed',
            code: 'VALIDATION_ERROR',
            details: err.details || err.message,
            timestamp: new Date().toISOString()
        };
        return res.status(400).json({ error });
    }

    if (err.name === 'UnauthorizedError' || err.message.includes('jwt')) {
        error = {
            message: 'Authentication failed',
            code: 'AUTH_ERROR',
            timestamp: new Date().toISOString()
        };
        return res.status(401).json({ error });
    }

    if (err.name === 'ForbiddenError') {
        error = {
            message: 'Access denied',
            code: 'ACCESS_DENIED',
            timestamp: new Date().toISOString()
        };
        return res.status(403).json({ error });
    }

    if (err.name === 'SequelizeUniqueConstraintError') {
        error = {
            message: 'Resource already exists',
            code: 'DUPLICATE_ERROR',
            field: err.errors?.[0]?.path,
            timestamp: new Date().toISOString()
        };
        return res.status(409).json({ error });
    }

    if (err.name === 'SequelizeForeignKeyConstraintError') {
        error = {
            message: 'Referenced resource not found',
            code: 'REFERENCE_ERROR',
            timestamp: new Date().toISOString()
        };
        return res.status(400).json({ error });
    }

    // Log unhandled errors
    console.error('Unhandled error:', err);

    res.status(500).json({ error });
};

module.exports = errorHandler;
```

### Custom Error Classes

```javascript
// utils/errors.js
class AuthError extends Error {
    constructor(message, code = 'AUTH_ERROR') {
        super(message);
        this.name = 'AuthError';
        this.code = code;
    }
}

class ValidationError extends Error {
    constructor(message, details = null) {
        super(message);
        this.name = 'ValidationError';
        this.details = details;
    }
}

class PermissionError extends Error {
    constructor(message, requiredPermission = null) {
        super(message);
        this.name = 'PermissionError';
        this.requiredPermission = requiredPermission;
    }
}

class RateLimitError extends Error {
    constructor(message = 'Rate limit exceeded') {
        super(message);
        this.name = 'RateLimitError';
    }
}

module.exports = {
    AuthError,
    ValidationError,
    PermissionError,
    RateLimitError
};
```

## Testing Strategy

### Unit Tests

```javascript
// tests/services/AuthService.test.js
const AuthService = require('../../services/AuthService');
const bcrypt = require('bcrypt');

describe('AuthService', () => {
    let authService;
    
    beforeEach(() => {
        authService = new AuthService();
    });

    describe('validateLogin', () => {
        test('should authenticate valid credentials', async () => {
            const mockUser = {
                user_id: 1,
                email: '<EMAIL>',
                password_hash: await bcrypt.hash('password123', 12),
                is_active: true,
                failed_login_attempts: 0,
                account_locked_until: null,
                mfa_enabled: false
            };

            // Mock database calls
            jest.spyOn(User, 'findOne').mockResolvedValue(mockUser);
            jest.spyOn(authService, 'handleSuccessfulLogin').mockResolvedValue();
            jest.spyOn(authService, 'generateTokens').mockResolvedValue({
                accessToken: 'mock-access-token',
                refreshToken: 'mock-refresh-token'
            });

            const result = await authService.validateLogin('<EMAIL>', 'password123');

            expect(result).toHaveProperty('user');
            expect(result).toHaveProperty('tokens');
            expect(result.tokens).toHaveProperty('accessToken');
        });

        test('should reject invalid credentials', async () => {
            jest.spyOn(User, 'findOne').mockResolvedValue(null);

            await expect(
                authService.validateLogin('<EMAIL>', 'wrongpassword')
            ).rejects.toThrow('Invalid credentials');
        });

        test('should handle account lockout', async () => {
            const lockedUser = {
                user_id: 1,
                email: '<EMAIL>',
                account_locked_until: new Date(Date.now() + 60000), // locked for 1 minute
                is_active: true
            };

            jest.spyOn(User, 'findOne').mockResolvedValue(lockedUser);
            jest.spyOn(authService, 'isAccountLocked').mockResolvedValue(true);

            await expect(
                authService.validateLogin('<EMAIL>', 'password123')
            ).rejects.toThrow('Account is temporarily locked');
        });
    });

    describe('handleFailedLogin', () => {
        test('should increment failed attempts', async () => {
            const user = {
                user_id: 1,
                failed_login_attempts: 2
            };

            const updateSpy = jest.spyOn(User, 'update').mockResolvedValue();

            await authService.handleFailedLogin(user);

            expect(updateSpy).toHaveBeenCalledWith(
                expect.objectContaining({
                    failed_login_attempts: 3
                }),
                expect.any(Object)
            );
        });

        test('should lock account after max attempts', async () => {
            process.env.MAX_LOGIN_ATTEMPTS = '5';
            
            const user = {
                user_id: 1,
                failed_login_attempts: 4 // This will be the 5th attempt
            };

            const updateSpy = jest.spyOn(User, 'update').mockResolvedValue();

            await authService.handleFailedLogin(user);

            expect(updateSpy).toHaveBeenCalledWith(
                expect.objectContaining({
                    failed_login_attempts: 5,
                    account_locked_until: expect.